import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function latLngFromString(str) {
  // possible variations
  // "[4.12345, -75.12345]"
  // "4.12345, -75.12345"

  //console.log("latLngFromString", str)

  if (!str || str.length == 0) return undefined
  if (str.indexOf("[") >= 0) return JSON.parse(str)

  let parts = str.split(",")
  //console.log("latLngFromString parts", parts)

  return [
    parseFloat(parts[0]), parseFloat(parts[1])
  ]
}

export function latLngFromPoint(str) {
  // possible variations
  // "POINT(-75.12345 4.12345)"   longitude first

  //console.log("latLngFromPoint", str)

  if (!str || str.length == 0) return undefined

  // replace POINT with nothing
  str = str.replace("POINT(", "")
  str = str.replace(")", "")

  let parts = str.split(" ")
  //console.log("latLngFromPoint parts", str, parts)

  return [
    parseFloat(parts[1]), parseFloat(parts[0])
  ]
}


export function round(value, precision) {
  let multi = 10
  precision -= 1

  while (precision > 0) {
    multi = multi * 10
    precision = precision - 1
  }

  return Math.round(value * multi) / multi
}

export const getRandomInt = function (max) {
  return Math.floor(Math.random() * Math.floor(max))
}


export const fixupPhone = (phone) => {
  if (!phone) return phone;

  phone = phone.replace(/\s/g, '')
  if (phone.startsWith('+')) return phone;

  if (phone.length === 10) return `+1${phone}`

  return `+${phone}`
}

export const currencyValue = (number) => {
  let result = new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(number);
  return result
}
