
const jquery = window.jQuery

import { signString } from "./md5";

export function generateNonce(length) {
  length ||= 32;

  var text = "";
  var possible =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  for (var i = 0; i < length; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}

export function signMap(user, map) {
  let nonce = generateNonce(32);

  //console.log("signString with", `${user.email}-${user.id}-${user.token}-${nonce}`)

  let token = user.token || user.api_key
 
  map["signerId"] = user.id;
  map["signature"] = signString(
    `${user.email}-${user.id}-${token}-${nonce}`
  );
  map["nonce"] = nonce;
}

export function getApiUrl(path) {
  // test for dev server
  if (window.location.host == "localhost:8080" ) {
    // vite dev server
     //return `https://dev.openzcan.com${path}`;   // uncomment to use the dev server
     return `http://localhost:7008${path}`; // using the docker image, comment out to use the dev server
  } else if (window.location.host == "localhost:7007" || window.location.host == "localhost:7008") {
    // local docker container possibly proxied by webpack dev server
    return `http://${window.location.host}${path}`;
  }

  // default to the location.host
  return `https://${window.location.host}${path}`;
}

function submitToApi(method, path, user, map, onSuccess, onError) {

  const $ = window.jQuery

  // sign the map with the user api key (token)
  signMap(user, map);

  const url = getApiUrl(path);

  //console.log("submit to API", method, url, map)

  const options = method === 'GET' ? {
    url: url,
    method: method,
    xhrFields: { withCredentials: true },
  } : {
    url: url,
    method: method,
    dataType: 'json',
    data: JSON.stringify(map),  // TODO check this doesn't break anything
    contentType: "application/json; charset=UTF-8",
    xhrFields: { withCredentials: true },
  }

  // submit to the URI denoted by path
  $.ajax(options)
    .then((data) => {
      if (onSuccess) onSuccess(data);
    })
    .catch((data) => {
      if (data.status == 200) {
        onSuccess(data)
      } else {
        console.log('error sending request', data)
        if (onError) onError(`${data.responseText} : ${data.statusText}`);
      }

    });
}

export function getFromApi(path, user, onSuccess, onError) {
  submitToApi("GET", path, user, {}, onSuccess, onError);
}

export function postToApi(path, user, map, onSuccess, onError) {
  submitToApi("POST", path, user, map, onSuccess, onError);
}

export function putToApi(path, user, map, onSuccess, onError) {
  submitToApi("PUT", path, user, map, onSuccess, onError);
}

export function deleteToApi(path, user, map, onSuccess, onError) {
  submitToApi("DELETE", path, user, map, onSuccess, onError);
}

export function patchToApi(path, user, map, onSuccess, onError) {
  submitToApi("PATCH", path, user, map, onSuccess, onError);
}

export function uploadPhoto(
  method,
  path,
  user,
  map,
  element,
  onProgress,
  onLoad,
  onSuccess,
  onError
) {

  const $ = window.jQuery

  var fileSelect = document.getElementById(element);
  var files = fileSelect.files;
  var formData = new FormData(); // https://developer.mozilla.org/en-US/docs/XMLHttpRequest/FormData
  const url = getApiUrl(path);
  /*
  FormData available on
  IE 10+
  Firefox 4.0+
  Chrome 7+
  Safari 5+
  Opera 12+
*/

  if (files.length == 0) {
    alert("Por favor seleccione una nueva foto");
    return;
  }

  var file = files[0];

  // Check the file type.
  if (!file.type.match("image.*")) {
    alert("La foto no es una imagen.");
    return;
  }

  if (file.size > 1 * 1024 * 1024) {
    alert(
      "Error : La foto es demasiado grande, cambie el tamaño a menos de 1 MB"
    );
    return;
  }

  // Add the file to the request.
  formData.append("photo", file, file.name);

  // sign the map with the user api key (token)
  signMap(user, map);

  Object.keys(map).forEach((key) => {
    formData.append(key, map[key]);
  });

  // send PUT request to server
  $.ajax({
    type: method,
    url: url,
    dataType: "json",
    xhr: function () {
      var myXhr = $.ajaxSettings.xhr();
      if (myXhr.upload) {
        myXhr.upload.addEventListener(
          "progress",
          function (e) {
            // upload progress event
            var percent_complete = (e.loaded / e.total) * 100;

            // Percentage of upload completed
            // console.log(percent_complete)

            let event = e;
            var percent = 0;
            var position = event.loaded || event.position;
            var total = event.total;
            var progress_bar_id = "#progress-wrp";
            if (event.lengthComputable) {
              percent = Math.ceil((position / total) * 100);
            }

            if (onProgress) onProgress(percent);
          },
          false
        );
      }
      // AJAX request finished event
      myXhr.addEventListener("load", function (e) {
        // HTTP status message
        //console.log('update logo finished', e.target)

        // request.response will hold the response from the server
        //console.log(myXhr.response)
        let res = JSON.parse(myXhr.response);

        if (onLoad) onLoad(res.result);
      });
      return myXhr;
    },
    success: function (data) {
      // set new asset
      //console.log('update logo success', data)
      if (onSuccess) onSuccess(data.result);

      fileSelect.value = null;
    },
    error: function (error) {
      // handle error

      if (onError) onError(error);
    },
    async: true,
    data: formData,
    cache: false,
    contentType: false,
    processData: false,
    timeout: 60000,
  });
}

export function postWithImage(
  path,
  user,
  map,
  element,
  onProgress,
  onLoad,
  onSuccess,
  onError
) {
  uploadPhoto(
    "POST",
    path,
    user,
    map,
    element,
    onProgress,
    onLoad,
    onSuccess,
    onError
  );
}

export function putWithImage(
  path,
  user,
  map,
  element,
  onProgress,
  onLoad,
  onSuccess,
  onError
) {
  uploadPhoto(
    "PUT",
    path,
    user,
    map,
    element,
    onProgress,
    onLoad,
    onSuccess,
    onError
  );
}

export function uploadFile(
  method,
  path,
  user,
  map,
  element,
  onProgress,
  onLoad,
  onSuccess,
  onError
) {

  const $ = window.jQuery

  var fileSelect = document.getElementById(element);
  var files = fileSelect.files;
  var formData = new FormData(); // https://developer.mozilla.org/en-US/docs/XMLHttpRequest/FormData
  const url = getApiUrl(path);
  /*
  FormData available on
  IE 10+
  Firefox 4.0+
  Chrome 7+
  Safari 5+
  Opera 12+
*/

  if (files.length == 0) {
    alert("Por favor seleccione un archivo");
    return;
  }

  var file = files[0];

  if (file.size > 1 * 1024 * 1024) {
    alert(
      "Error : El archivo es demasiado grande, cambie el tamaño a menos de 1 MB"
    );
    return;
  }

  // Add the file to the request.
  formData.append("photo", file, file.name);

  // sign the map with the user api key (token)
  signMap(user, map);

  Object.keys(map).forEach((key) => {
    formData.append(key, map[key]);
  });

  // send PUT request to server
  $.ajax({
    type: method,
    url: url,
    dataType: "json",
    xhr: function () {
      var myXhr = $.ajaxSettings.xhr();
      if (myXhr.upload) {
        myXhr.upload.addEventListener(
          "progress",
          function (e) {
            // upload progress event
            var percent_complete = (e.loaded / e.total) * 100;

            // Percentage of upload completed
            // console.log(percent_complete)

            let event = e;
            var percent = 0;
            var position = event.loaded || event.position;
            var total = event.total;
            var progress_bar_id = "#progress-wrp";
            if (event.lengthComputable) {
              percent = Math.ceil((position / total) * 100);
            }

            if (onProgress) onProgress(percent);
          },
          false
        );
      }
      // AJAX request finished event
      myXhr.addEventListener("load", function (e) {
        // HTTP status message
        //console.log('update logo finished', e.target)

        // request.response will hold the response from the server
        //console.log(myXhr.response)
        let res = JSON.parse(myXhr.response);

        if (onLoad) onLoad(res.result);
      });
      return myXhr;
    },
    success: function (data) {
      // set new asset
      //console.log('update logo success', data)
      if (onSuccess) onSuccess(data.result);

      fileSelect.value = null;
    },
    error: function (error) {
      // handle error

      if (onError) onError(error);
    },
    async: true,
    data: formData,
    cache: false,
    contentType: false,
    processData: false,
    timeout: 60000,
  });
}

export function uploadGivenFile(
  method,
  path,
  user,
  map,
  name,
  file,
  onProgress,
  onLoad,
  onSuccess,
  onError
) {


  const $ = window.jQuery

  var formData = new FormData(); // https://developer.mozilla.org/en-US/docs/XMLHttpRequest/FormData
  const url = getApiUrl(path);
  /*
  FormData available on
  IE 10+
  Firefox 4.0+
  Chrome 7+
  Safari 5+
  Opera 12+
*/
   // Add the file to the request.
   formData.append(name, file, file.name);

  // sign the map with the user api key (token)
  signMap(user, map);

  Object.keys(map).forEach((key) => {
    formData.append(key, map[key]);
  });
    // send PUT request to server
    $.ajax({
      type: method,
      url: url,
      dataType: "json",
      xhr: function () {
        var myXhr = $.ajaxSettings.xhr();
        if (myXhr.upload) {
          myXhr.upload.addEventListener(
            "progress",
            function (e) {
              // upload progress event
              var percent_complete = (e.loaded / e.total) * 100;

              // Percentage of upload completed
              // console.log(percent_complete)

              let event = e;
              var percent = 0;
              var position = event.loaded || event.position;
              var total = event.total;
              var progress_bar_id = "#progress-wrp";
              if (event.lengthComputable) {
                percent = Math.ceil((position / total) * 100);
              }

              if (onProgress) onProgress(percent);
            },
            false
          );
        }
        // AJAX request finished event
        myXhr.addEventListener("load", function (e) {
          // HTTP status message
          //console.log('update logo finished', e.target)

          // request.response will hold the response from the server
          //console.log(myXhr.response)
          let res = JSON.parse(myXhr.response);

          if (onLoad) onLoad(res.result);
        });
        return myXhr;
      },
      success: function (data) {
        // set new asset
        //console.log('uploadGivenFile success', data)
        if (onSuccess) onSuccess(data);
      },
      error: function (error) {
        // handle error

        if (onError) onError(error);
      },
      async: true,
      data: formData,
      cache: false,
      contentType: false,
      processData: false,
      timeout: 60000,
    });
}