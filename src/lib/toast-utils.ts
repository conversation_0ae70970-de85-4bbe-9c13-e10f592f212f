import { toast as sonnerToast } from "sonner";
import { toast as useToastHook } from "@/hooks/use-toast";

type ToastType = "default" | "success" | "error" | "warning" | "info";

/**
 * Show a toast notification using either <PERSON><PERSON> or Shadcn UI toast
 * @param message The message to display
 * @param type The type of toast (default, success, error, warning, info)
 * @param options Additional options for the toast
 */
export function showToast(
  message: string, 
  type: ToastType = "default", 
  options: Record<string, any> = {}
) {
  // Use Sonner toast by default (more modern UI)
  if (options.useShadcn) {
    // Use Shadcn UI toast if specifically requested
    useToastHook({
      title: options.title || getDefaultTitle(type),
      description: message,
      variant: type === "error" ? "destructive" : "default",
      ...options
    });
  } else {
    // Use Sonner toast (simpler API)
    switch (type) {
      case "success":
        sonnerToast.success(message, options);
        break;
      case "error":
        sonnerToast.error(message, options);
        break;
      case "warning":
        sonnerToast.warning(message, options);
        break;
      case "info":
        sonnerToast.info(message, options);
        break;
      default:
        sonnerToast(options.title || "", message, options);
    }
  }
}

function getDefaultTitle(type: ToastType): string {
  switch (type) {
    case "success": return "Success";
    case "error": return "Error";
    case "warning": return "Warning";
    case "info": return "Information";
    default: return "";
  }
}

// Export both toast implementations for direct access if needed
export { sonnerToast, useToastHook };