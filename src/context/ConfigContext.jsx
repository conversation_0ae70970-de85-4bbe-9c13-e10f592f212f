import React, { useState, useEffect, createContext } from "react";
import { deleteToApi, putToApi, postToApi, getFrom<PERSON><PERSON> } from "../lib/api";

export const ConfigContext = createContext();

export const ConfigProvider = ({ children }) => {

  const [user, setUser] = useState(null)
  const [config, setConfig] = useState([])
  const [defaults, setDefaults] = useState([])

  useEffect(() => {
    // load the user from local storage and get the data
    getFromApi("/api/v1/private/user/whoami", {},
      (response) => {
        let result = response.result
        setUser(result)

        //console.log("ConfigContext user", result)
      }, (error) => {
        console.log(error);
      })

  }, []);

  const getConfig = (user, location, callback) => {
    // get the config for the location
    const url = `/api/v1/admin/config/location/${location.id}`;

    // get the config from the API
    postToApi(
      url,
      user,
      {},
      (response) => {

        let res = response.result;
        //let latlon = latLngFromString(location.latlng)
        //let here = LatLon(latlon[0], latlon[1]);

        //console.log("config result", res);

        setConfig(res);

        if (callback) {
          callback(res)
        }

      },
      (error) => {
        console.log(error);
      }
    );

    getFromApi("/api/v1/config/default_config", {},
      (response) => {
        let result = response.result
        setDefaults(result)

        //console.log("default config", result)
      }, (error) => {
        console.log(error);
      })
  }

  const defaultConfigValue = (key, model, defValue) => {
    if (defaults) {
      let cfg = defaults.find(c => c.model == model && c.name == key)
      if (cfg) {
        //console.log("return config value", key, cfg.value, defValue);
        return cfg.value
      }
    }
    // try up the config tree [product|service|campaign] -> menu -> location -> business
    if (["product", "service", "campaign"].includes(model)) {
      return defaultConfigValue(key, "menu", defValue)
    } else if (model == "menu") {
      return defaultConfigValue(key, "location", defValue)
    } else if (model == "location") {
      return defaultConfigValue(key, "business", defValue)
    }
    //console.log("return default value", key, defValue);

    return defValue;
  }

  const modelConfigValue = (key, model, location, defValue) => {
    if (config && location) {
      let cfg = config.find(c => c.model == model && c.name == key && c.locationId == location.id)
      if (cfg) {
        //console.log("return config value", key, cfg, defValue);
        return cfg.value
      }
    }
    //console.log("failed to find", key, model, config)
    // try up the config tree [product|service|campaign] -> menu -> location
    if (["product", "service", "campaign"].includes(model)) {
      return modelConfigValue(key, "menu", location, defValue)
    } else if (model == "menu") {
      return modelConfigValue(key, "location", location, defValue)
    }

    return defValue;
  }

  const updateConfigValue = (business, location, value, name, kind, description) => {

    console.log("update value", name, value, kind)

    // if the value is empty ignore it
    if ((!value || value == "") && kind != "bool") {
      return
    }

    let old = config.find(c => c.name == name && c.locationId == location.id);

    // if the config value does not exist create it via the API
    if (!old) {
      const url = `/api/v1/admin/config`;
      let map = {
        name: name,
        value: `${value}`,
        model: "location",
        userId: user.id,
        businessId: business.id,
        locationId: location.id,
        kind: kind,
        description: description
      }

      console.log("create new config", map)

      postToApi(
        url,
        user,
        map,
        (response) => {
          //console.log("create config response", response);
          let cfg = response.result;

          setConfig(config.concat(cfg));
        },
        (error) => {
          console.log(error);
        }
      );
    } else {
      // if the config value has changed then update it via the API
      console.log("update value", old, value)
      if (old.value != value) {
        const url = `/api/v1/admin/config/${old.id}`;

        old.value = `${value}`,

          putToApi(
            url,
            user,
            old,
            (response) => {
              //console.log("config updated", response);
              setConfig(config.slice());
            },
            (error) => {
              console.log(error);
            }
          );
      } else {
        console.log("no change", name, value)
      }
    }
  }

  const intConfigValue = (key, model, location, defValue) => {
    if (config && location) {
      let cfg = config.find(c => c.model == model && c.name == key && c.locationId == location.id)
      if (cfg) {
        //console.log("return config value", key, cfg, defValue);
        if (cfg.value && typeof cfg.value == 'number')
          return cfg.value

        if (cfg.value && typeof cfg.value == 'string')
          return parseInt(cfg.value)
      }
    }

    let val = defaultConfigValue(key, model, defValue)

    if (val && typeof val == 'number')
      return val

    if (val && typeof val == 'string')
      return parseInt(val);

    return defValue
  }

  const deleteConfigValue = (name) => {
    let old = config.find(c => c.name == name);

    if (old) {
      console.log("deleting config", old)

      if (old.userId != user.id && (user.id != 1 && user.id != 194)) {
        alert(
          tr("Permission denied") + ".  " +
          tr("Invalid user") + ".  " +
          trmap("You are not the owner of this {OBJECT}", {
            OBJECT: tr("Config setting")
          })  + ".  " +
          trmap("The owner user ID is {USERID}",
          {USERID: old.userId})

          )
          return
      }
      const url = `/api/v1/admin/config/${old.id}`;

      deleteToApi(url, user, {},  (response) => {
        //console.log("config deleted", response);
        setConfig(config.filter((c) => c.id != old.id));
      },
      (error) => {
        console.log(error);
      })
    } else {
      console.log("no config for", name, config)
    }
  }

  return (
    <ConfigContext.Provider
      value={{
        config,
        defaults,
        getConfig,
        setConfig,
        modelConfigValue,
        defaultConfigValue,
        deleteConfigValue,
        intConfigValue,
        updateConfigValue,
        setUser,
        user
      }}
    >
      {children}
    </ConfigContext.Provider>
  )
}
