import React, { useState, useEffect, createContext } from "react";
import { putToApi, postToApi, getFrom<PERSON>pi, deleteToApi } from "@/lib/api";


export const TeamContext = createContext();

export const TeamProvider = ({ children }) => {

  const [businessTeam, setBusinessTeam] = useState([])
  const [locationTeam, setLocationTeam] = useState([])
  const [list, setList] = useState([])

  const getBusinessTeam = (user, business, location) => {
    // get the team for the business
    const url = `/api/v1/business/${business.id}/roles`;

    // get the team from the API
    getFromApi(
      url,
      user,
      (response) => {

        let res = response.result;

        //console.log("getBusinessTeam result", res, location);
        setTeams(res, location)
      },
      (error) => {
        console.log(error);
        // show sweet alert error popup
      }
    );
  }

  const getLocationTeam = (user, location) => {
    // get the team for the location
    const url = `/api/v1/business/${location.businessId}/roles/location/${location.id}`;

    // get the team from the API
    getFromApi(
      url,
      user,
      (response) => {

        let res = response.result;

        //console.log("team result", res);

        setLocationTeam(res);

      },
      (error) => {
        console.log(error);
        // show sweet alert error popup
      }
    );
  }

  const setTeams = (list, location) => {

    // seperate the team into business and location teams
    let bizTeam = list.filter((m) => m.locationId == 0 || m.type == 'Partner')
    setBusinessTeam(bizTeam);

    let locTeam = list.filter((m) => m.locationId == location.id && m.type != 'Partner')

    setLocationTeam(locTeam)
    setList(list)

    //console.log("set teams", bizTeam, locTeam, list, location)
  }

  const updateLocationTeam = (location) => {
    console.log("set location", location, businessTeam)
    setLocationTeam(list.filter((m) => m.locationId == location.id))
  }

  const mergeLists = (team1, team2) => {
    return team1.concat(team2.filter((t) => team1.find((t1) => t1.id == t.id) == undefined))
  }

  const onSuccess = (result) => {
    console.log("success", result)
  }
  const onError = (error) => {
    console.log("error", error)
  }

  // user object is the signed in user managing the team via web admin
  // member is the new team member
  const addTeamMember = (user, member, location) => {

    let url = `/api/v1/business/${member.businessId}/role`

    console.log("add team", member, location)

    postToApi(url, user, member,
      (data) => {

        console.log("result", data)
        onSuccess(`New member Added`)
        let list = [...mergeLists(businessTeam, locationTeam), data.result]
        setTeams(list, location)
      },
      // show sweet alert success popup
      (error) => {

        onError(`Can't add this member`)
      }
      // show sweet alert error popup
    )
  }

  const updateTeamMember = (user, member) => {
    let url = `/api/v1/business/role/${member.id}`

    putToApi(url, user, member,
      (result) => {
        onSuccess(`Member Updated`)
      },
      // show sweet alert success popup
      (error) => {
        onError(`Can't update this member`)
      }
      // show sweet alert error popup
    )
  }

  const deleteTeamMember = (user, member, location) => {
    let url = `/api/v1/business/${member.businessId}/role/${member.id}`

    deleteToApi(url, user, member,
      (result) => {
        onSuccess(`Member Deleted`)
        let list = [...mergeLists(businessTeam, locationTeam)].filter((m) => m.id != member.id)
        setTeams(list, location)
      },
      // show sweet alert success popup
      (error) => {
        onError(`Can't delete this member`)
      }
      // show sweet alert error popup
    )
  }

  return (
    <TeamContext.Provider
      value={{
        businessTeam,
        locationTeam,
        getBusinessTeam,
        getLocationTeam,
        updateLocationTeam,
        addTeamMember,
        updateTeamMember,
        deleteTeamMember
      }}
    >
      {children}
    </TeamContext.Provider>
  )
}
