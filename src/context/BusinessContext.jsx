import React, { createContext, useContext, useState, useEffect } from 'react';
import { getFromApi, postToApi } from '../lib/api';
import LatLon from '../lib/latlon';

export const BusinessContext = createContext();

export const BusinessProvider = ({ children }) => {

  const [departments, setDepartments] = useState([]);
  const [cities, setCities] = useState([]);
  const [businesses, setBusinesses] = useState([]);
  const [customers, setCustomers] = useState([]);
  const [business, setBusiness] = useState(null)
  const [provider, setProvider] = useState(null)
  const [location, setLocation] = useState(null)
  const [selectedProvince, setselectedProvince] = useState('');
  const [selectedCity, setSelectedCity] = useState('');
  const [country, setCountry] = useState('USA');
  const [distinctList, setDistinctList] = useState(null);
  const [currentLocation, setCurrentLocation] = useState(null)

  const [contractors, setContractors] = useState([])
  const [contractor, setContractor] = useState(null)
  const [contractorUsers, setContractorUsers] = useState([])

  function getDistance(here, biz) {
    if (!biz.latlng) {
      return ''
    }

    let latlon2 = LatLon.fromString(biz.latlng)
    let meters = Math.round(here.distanceTo(latlon2))

    return meters  // > 1000 ? (meters / 1000).toFixed(1) + ' km' : meters + ' m'
  }

  useEffect(() => {

    //console.log("BusinessProvider useEffect",provider, businesses)


  }, [provider, businesses]);

  const getDistinctLocations = () => {

    getFromApi('/api/v1/admin/location/distinct_list/USA', {}, (data) => {
      //console.log("getDistinctLocations", data)
      setDistinctList(data.result)

      let departments = [];
      data.result.forEach(element => {
        element.province = element.province.charAt(0).toUpperCase() + element.province.slice(1);;
        if (!departments.includes(element.province))
          departments.push(element.province);
      });

      //console.log("getDistinctLocations set departments", department)
      departments.sort((a, b) => a.localeCompare(b))

      setDepartments(departments)

      // get the cities for the current department
      if (selectedProvince) {
        let cityList = data.result.filter(x => x.province === selectedProvince).map((x) => x.city);
        //console.log("getDistinctLocations set cities", department, cityList)
        cityList.sort((a, b) => a.localeCompare(b))
        setCities(cityList);
      }

    }, (error) => {
      console.log('getDistinctLocations failed', error)
    })
  }

  const setProvince = (name) => {

    // extract the city from the distinct data
    if (name) {
      let citylist = distinctList.filter(x => x.province === name).map((obj) => { return obj.city });
      citylist.sort((a, b) => a.localeCompare(b))
      setselectedProvince(name);
      setCities(citylist);
    } else {
      setselectedProvince(null);
      setCities(null)
    }

    //setSelectedCity(citylist[0]);
    /*
 let firstCity = citylist[0]

    getBusinessesForCity({
      city: firstCity,
      province: departmentName,
      department: departmentName,
      country: country,
      latitude: firstCity.latitude,
      longitude: firstCity.longitude,
    })
    */



    // let businessList = [];
    // getFromApi(`/api/v1/business/city/${citylist[0]}/${departmentName}/USA`, {}, (data) => {
    //   //console.log("getCitiesApi result", data)
    //   data.result.forEach(element => {
    //     if (businessList.find(x => x.name.toLowerCase().replace(/\s/g, '') === element.name.toLowerCase().replace(/\s/g, '')) === undefined) businessList.push(element);
    //   });

    //   //console.log("getCitiesApi businesses", businessList)
    //   setBusinesses(businessList)
    // }, (error) => {
    //   console.log('failed', error)
    // })


  }


  const _getBusinessesForUrl = (url) => {
    let businessList = [];

    getFromApi(url, {}, (data) => {
      //console.log("getBusinessesForCity result", data)
      data.result.forEach(element => {
        if (businessList.find(x => x.name.toLowerCase().replace(/\s/g, '') === element.name.toLowerCase().replace(/\s/g, '')) === undefined) businessList.push(element);
      });

      // sort the restaurants by subscription level then order balance
      businessList.sort((a, b) => {
        return a.name.localeCompare(b.name);
      })

      //mapBusinessMenus(businessList, data.menus)

      //console.log("getBusinessesForCity businesses", businessList)
      setBusinesses(businessList)

    }, (error) => {
      console.log('failed', error)
    })
  }

  const getBusinessesForCity = (location) => {
    //console.log("getBusinessesForCity", location)

    if (provider != null) {
      // we are a provider user so filter the list of customers
      let businessList = customers.filter((b) => b.city == location.city)
      setBusinesses(businessList)

      return
    }

    // if we are given a location, use it to get the businesses for the city
    // and calculate the distance from the current location

    // otherwise get the businesses for the city without distance
    let businessList = [];

    let url = `/api/v1/admin/business/city/${location.city}/${location.province || location.department}/${location.country}`
    //console.log("getBusinessesForCity from", url)

    _getBusinessesForUrl(url)

  }

  const searchBusinessByName = (term) => {

    if (term.length < 3) return

    let url = `/api/v1/admin/business/search_by_name/${term}/${country}`
    //console.log("searchBusinessByName from", url)

    _getBusinessesForUrl(url)
  }

  const getBusinessesForProvince = (location) => {
    //console.log("getBusinessesForProvince", location)
    // if we are given a location, use it to get the businesses for the city
    // and calculate the distance from the current location

    // otherwise get the businesses for the city without distance
    let businessList = [];

    let url = `/api/v1/admin/business/province/${location.province || location.department}/${location.country}`
    //console.log("getBusinessesForProvince from", url)

    getFromApi(url, {}, (data) => {
      //console.log("getBusinessesForProvince result", data)
      data.result.forEach(element => {
        if (businessList.find(x => x.name.toLowerCase().replace(/\s/g, '') === element.name.toLowerCase().replace(/\s/g, '')) === undefined)
          businessList.push(element);
      });

      // sort the restaurants by subscription level then order balance
      businessList.sort((a, b) => {
        return a.name.localeCompare(b.name);
      })

      //mapBusinessMenus(businessList, data.menus)

      //console.log("getBusinessesForCity businesses", businessList)
      setBusinesses(businessList)

    }, (error) => {
      console.log('failed', error)
    })
  }

  const getCustomersforProvider = (provider) => {
    // get the customer businesses for the provider
    getFromApi(`/api/v1/provider/${provider.id}/customers`, provider, (data) => {
      //console.log("getProviderData customers", data)
      setBusinesses(data.result)
      setCustomers(data.result)

      let results = [];

      data.result.forEach(element => {
        if (!results.find(x => x.city === element.city && x.province === element.province)) {
          results.push({
            city: element.city,
            province: element.province,
            latlng: element.latlng
          })
        }
      })

      setDistinctList(results)

      let departments = [];
      data.result.forEach(element => {
        element.province = element.province.charAt(0).toUpperCase() + element.province.slice(1);;
        if (!departments.includes(element.province))
          departments.push(element.province);
      });
 
      departments.sort((a, b) => a.localeCompare(b))

      setDepartments(departments)

    }, (error) => {
      console.log('failed', error)
    })
  }

  const setupUser = (user) => {
    // if this is a provider user get the provider customer data
    if (user && user.roles && user.roles.includes("provider") && provider == null) {
      getProviderData(user)
    }  else if (user && user.roles && user.roles.includes("customer")) {

    }
  }

  const getContractorsForProvider = (provider) => {
    getFromApi(`/api/v1/contractors/${provider.id}`, provider, (data) => {
      //console.log("getContractorsForProvider", data)
      setContractors(data.result.contractors)
      setContractorUsers(data.result.users)
    }, (error) => {
      console.log('failed', error)
    })
  }

  const getProviderData = (user) => {
    // get the provider business
    getFromApi(`/api/v1/provider/${user.id}/business`, user, (data) => {
      //console.log("getProviderData business", data.result)
      setProvider(data.result)

      if (data.result) {
        let locations = data.result.locations

        if (locations && locations.length == 1) {
          setLocation(locations[0])
        }
      }

      getCustomersforProvider(data.result)
      getContractorsForProvider(data.result)
    }, (error) => {
      console.log('failed', error)
    })
  }

  const addContact = (deliveryData, menuData) => {

    let map = {
      id: 0,
      businessId: menuData.businessId,
      locationId: menuData.locationId,
      name: deliveryData.name,
      phone: deliveryData.phone,
      email: deliveryData.email,
      city: deliveryData.city,
      province: deliveryData.province,
      country: deliveryData.country,
      is_client: false
    }

    postToApi("/api/v1/contact/menu", { id: 0 }, map, (result) => {

    }, (error) => {
      console.log("error adding contact", error)
    })
  }

  return (
    <BusinessContext.Provider
      value={{

        // selected data
        business,
        setBusiness,
        setBusinesses,

        location,
        setLocation,

        // data
        businesses,
        cities,

        selectedCity,
        selectedProvince,
        departments,

        // functions
        addContact,

        configValue: (business, name, defaultValue) => {
          if (business.Configs && business.Configs.length > 0) {
            let cfg = business.Configs.find((cfg) => cfg.name === name)
            if (cfg) return cfg.value
          }
          return defaultValue
        },

        locationConfigValue: (configs, id, name, defaultValue) => {
          if (configs && configs.length > 0) {
            let cfg = configs.find((cfg) => cfg.locationId === id && cfg.name === name)
            if (cfg) return cfg.value
          }
          return defaultValue
        },
        locationConfigBool: (configs, id, name, defaultValue) => {
          if (configs && configs.length > 0) {
            let cfg = configs.find((cfg) => cfg.locationId === id && cfg.name === name)
            if (cfg) return cfg.value === "true"
          }
          return defaultValue
        },

        configNumber: (business, name, defaultValue) => {
          if (business.Configs && business.Configs.length > 0) {
            let cfg = business.Configs.find((cfg) => cfg.name === name)
            if (cfg && cfg.value.length > 0) {
              try {
                return parseInt(cfg.value)
              } catch (ex) {
                console.log("could not parse int value", cfg.value)
              }
            }
          }
          return defaultValue
        },

        locationConfigNumber: (configs, id, name, defaultValue) => {
          if (configs && configs.length > 0) {
            let cfg = configs.find((cfg) => cfg.locationId === id && cfg.name === name)
            if (cfg && cfg.value.length > 0) {
              try {
                return parseInt(cfg.value)
              } catch (ex) {
                console.log("could not parse int value", cfg.value)
              }
            }
          }
          return defaultValue
        },

        getDistinctLocations,
        getBusinessesForCity,
        getBusinessesForProvince,
        getCustomersforProvider,
        getContractorsForProvider,
        contractors,
        contractorUsers,

        currentLocation,
        setCurrentLocation,
        //setselectedProvince,
        //setSelectedCity,
        setCountry,

        getProviderData,
        provider,


        setProvince,
        setCity: (name) => {
          setSelectedCity(name);

          if (provider && name) {
            // filter businesses for city
            let businessList = customers.filter((b) => b.city == name)
            setBusinesses(businessList)
          } else if (provider) {
            // get all businesses
            setBusinesses(customers)
          } else if (name) {
            getBusinessesForCity({
              city: name, department: selectedProvince,
              country: country
            })
          }
        },
        searchBusinessByName,
        setupUser

      }}
    >
      {children}
    </BusinessContext.Provider>
  )
}

