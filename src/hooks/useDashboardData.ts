import { useState, useEffect, useContext } from 'react';
import { getFromApi } from '@/lib/api';
import { ConfigContext } from '@/context/ConfigContext';
import { BusinessContext } from '@/context/BusinessContext';
import { isDateInRange } from '@/components/dashboard/TimeFilter';
import { IWorkOrderRes, IInvoiceRes, ICustomerRes, IProviderRes, ISessionUser } from '@/types';

interface DashboardMetrics {
  workOrders: {
    active: number;
    completed: number;
    previousCompleted: number;
    scheduled: number;
    previousScheduled: number;
    total: number;
    previousTotal: number;
    statusBreakdown: { [key: string]: number };
  };
  technicians: {
    efficiency: Array<{
      name: string;
      ordersCompleted: number;
      hoursWorked: number;
      efficiency: number;
      previousEfficiency?: number;
    }>;
  };
  invoices: {
    total: number;
    paid: number;
    previousPaid: number;
    unpaid: number;
    previousUnpaid: number;
    overdue: number;
    previousOverdue: number;
    totalValue: number;
    previousTotalValue: number;
    paidValue: number;
    previousPaidValue: number;
    unpaidValue: number;
    previousUnpaidValue: number;
    statusBreakdown: { [key: string]: number };
  };
  customers: {
    total: number;
  }
}

interface UseDashboardDataReturn {
  metrics: DashboardMetrics;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useDashboardData(timeFilter: string = '30d'): UseDashboardDataReturn {
  const { user }: { user: ISessionUser } = useContext(ConfigContext);
  const { provider, businesses }: {
    provider: IProviderRes;
    businesses: ICustomerRes[];
  } = useContext(BusinessContext);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [workOrders, setWorkOrders] = useState<IWorkOrderRes[]>([]);
  const [invoices, setInvoices] = useState<IInvoiceRes[]>([]);
  const [metrics, setMetrics] = useState<DashboardMetrics>({
    workOrders: {
      active: 0,
      completed: 0,
      previousCompleted: 0,
      scheduled: 0,
      previousScheduled: 0,
      total: 0,
      previousTotal: 0,
      statusBreakdown: {}
    },
    technicians: {
      efficiency: []
    },
    invoices: {
      total: 0,
      paid: 0,
      previousPaid: 0,
      unpaid: 0,
      previousUnpaid: 0,
      overdue: 0,
      previousOverdue: 0,
      totalValue: 0,
      previousTotalValue: 0,
      paidValue: 0,
      previousPaidValue: 0,
      unpaidValue: 0,
      previousUnpaidValue: 0,
      statusBreakdown: {}
    },
    customers: {
      total: 0
    }
  });

  useEffect(() => {
    console.log("Metrics", metrics);
  }, [metrics]);

  useEffect(() => {
    if (!workOrders.length || !invoices.length || !businesses.length) return;
    const calculatedMetrics = calculateMetrics(workOrders, invoices, businesses);
    setMetrics(calculatedMetrics);
  }, [workOrders, invoices, businesses]);

  const fetchMetrics = () => {
    if (!user || !provider) return [];
    const urlWorkOrders = `/api/v1/provider/${provider.id}/work_orders/90`;
    getFromApi(urlWorkOrders, user,
      (response) => {
        setWorkOrders(response.result || []);
      },
      (error) => {
        console.error('Error fetching work orders:', error);
        setError(error);
      }
    );

    const urlInvoices = `/api/v1/provider/${provider.id}/invoices/90`;
    getFromApi(urlInvoices, user,
      (response) => {
        setInvoices(response.result || []);
      },
      (error) => {
        console.error('Error fetching invoices:', error);
        setError(error);
      }
    );
  }

  const calculateMetrics = (
    workOrders: IWorkOrderRes[],
    invoices: IInvoiceRes[],
    customers: ICustomerRes[]
  ) => {
    const filteredWorkOrders = workOrders.filter(wo =>
      isDateInRange(wo.CreatedAt, timeFilter)
    );
    const previousFilteredWorkOrders = workOrders.filter(wo =>
      isDateInRange(wo.CreatedAt, timeFilter, true)
    );

    // Calculate work order metrics
    const workOrderMetrics = {
      active: workOrders.filter(wo =>
        ['in progress', 'pending'].includes(wo.status?.toLowerCase() || '')
      ).length,

      completed: filteredWorkOrders.filter(wo =>
        wo.status?.toLowerCase() === 'completed'
      ).length,

      previousCompleted: previousFilteredWorkOrders.filter(wo =>
        wo.status?.toLowerCase() === 'completed'
      ).length,

      scheduled: workOrders.filter(wo =>
        wo.status?.toLowerCase() === 'scheduled'
      ).length,

      previousScheduled: previousFilteredWorkOrders.filter(wo =>
        wo.status?.toLowerCase() === 'scheduled'
      ).length,

      total: filteredWorkOrders.length,
      previousTotal: previousFilteredWorkOrders.length,

      statusBreakdown: filteredWorkOrders.reduce((acc, wo) => {
        const status = wo.status;
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number })
    };

    // Calculate technician metrics
    let techniciansWorks = filteredWorkOrders.reduce((acc, wo) => {
      if (wo.status?.toLowerCase() !== 'completed') return acc;
      wo.TeamMembers.forEach(tm => {
        acc[tm.user.id] = acc[tm.user.id] || { name: tm.user.name, ordersCompleted: 0, hoursWorked: 0 };
        acc[tm.user.id].ordersCompleted++;
        // For now, assume 8 hours per completed work order as mock data
        // In real implementation, this would come from time tracking data
        acc[tm.user.id].hoursWorked += 8;
      });
      return acc;
    }, {} as { [key: number]: { name: string; ordersCompleted: number; hoursWorked: number } });

    // Calculate previous period technician data for trends
    let previousTechniciansWorks = previousFilteredWorkOrders.reduce((acc, wo) => {
      if (wo.status?.toLowerCase() !== 'completed') return acc;
      wo.TeamMembers.forEach(tm => {
        acc[tm.user.id] = acc[tm.user.id] || { name: tm.user.name, ordersCompleted: 0, hoursWorked: 0 };
        acc[tm.user.id].ordersCompleted++;
        acc[tm.user.id].hoursWorked += 8; // Mock hours
      });
      return acc;
    }, {} as { [key: number]: { name: string; ordersCompleted: number; hoursWorked: number } });

    const technicianMetrics = {
      efficiency: Object.values(techniciansWorks).map(tm => {
        // Calculate efficiency as orders completed per hour (as percentage)
        const currentEfficiency = tm.hoursWorked > 0 ?
          Math.round((tm.ordersCompleted / tm.hoursWorked) * 100) : 0;

        // Calculate previous efficiency for trend
        const previousTech = previousTechniciansWorks[Object.keys(techniciansWorks).find(id =>
          techniciansWorks[parseInt(id)].name === tm.name) as any];
        const previousEfficiency = previousTech && previousTech.hoursWorked > 0 ?
          Math.round((previousTech.ordersCompleted / previousTech.hoursWorked) * 100) : 0;

        return {
          name: tm.name,
          ordersCompleted: tm.ordersCompleted,
          hoursWorked: tm.hoursWorked,
          efficiency: currentEfficiency,
          previousEfficiency: previousEfficiency
        };
      })
    };

    // Calculate invoice metrics
    const filteredInvoices = invoices.filter(inv =>
      isDateInRange(inv.CreatedAt, timeFilter)
    );
    const previousFilteredInvoices = invoices.filter(inv =>
      isDateInRange(inv.CreatedAt, timeFilter, true)
    );

    const invoiceMetrics = {
      total: filteredInvoices.length,

      paid: filteredInvoices.filter(inv => inv.Paid || inv.status?.toLowerCase() === 'paid').length,
      previousPaid: previousFilteredInvoices.filter(inv => inv.Paid || inv.status?.toLowerCase() === 'paid').length,

      unpaid: filteredInvoices.filter(inv => !inv.Paid && inv.status?.toLowerCase() !== 'paid').length,
      previousUnpaid: previousFilteredInvoices.filter(inv => !inv.Paid && inv.status?.toLowerCase() !== 'paid').length,
      
      overdue: filteredInvoices.filter(inv => {
        if (inv.Paid || inv.status?.toLowerCase() === 'paid') return false;
        const dueDate = new Date(inv.dueOn);
        return dueDate < new Date();
      }).length,

      previousOverdue: previousFilteredInvoices.filter(inv => {
        if (inv.Paid || inv.status?.toLowerCase() === 'paid') return false;
        const dueDate = new Date(inv.dueOn);
        return dueDate < new Date();
      }).length,

      totalValue: filteredInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
      previousTotalValue: previousFilteredInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),

      paidValue: filteredInvoices
        .filter(inv => inv.Paid || inv.status?.toLowerCase() === 'paid')
        .reduce((sum, inv) => sum + (inv.total || 0), 0),
      previousPaidValue: previousFilteredInvoices
        .filter(inv => inv.Paid || inv.status?.toLowerCase() === 'paid')
        .reduce((sum, inv) => sum + (inv.total || 0), 0),

      unpaidValue: filteredInvoices
        .filter(inv => !inv.Paid && inv.status?.toLowerCase() !== 'paid')
        .reduce((sum, inv) => sum + (inv.total || 0), 0),
      previousUnpaidValue: previousFilteredInvoices
        .filter(inv => !inv.Paid && inv.status?.toLowerCase() !== 'paid')
        .reduce((sum, inv) => sum + (inv.total || 0), 0),

      statusBreakdown: filteredInvoices.reduce((acc, inv) => {
        const status = inv.status || (inv.Paid ? 'paid' : 'unpaid');
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number })
    };

    return {
      workOrders: workOrderMetrics,
      invoices: invoiceMetrics,
      customers: { total: customers.length },
      technicians: technicianMetrics
    };
  };

  const fetchData = async () => {
    if (!user || !provider) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    fetchMetrics()
    setLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [user, provider, timeFilter]);

  return {
    metrics,
    loading,
    error,
    refetch: fetchData
  };
}