import React, { useContext, useState, useEffect } from 'react';
import { ConfigContext } from "@/context/ConfigContext"; 
import { getFromApi, postToApi } from '@/lib/api';
import QRCode from 'qrcode.react';
import { getRandomInt } from '@/lib/utils';
import { showToast } from "@/lib/toast-utils";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle, 
  CardDescription 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  User, 
  Mail, 
  MapPin, 
  Phone, 
  Globe, 
  Shield, 
  RefreshCw,
  Smartphone
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";

export function AccountPage() {
  const { user } = useContext(ConfigContext);
  const [code, setCode] = useState(null);

  useEffect(() => {
    setCode(user.unlockToken);
  }, [user]);

  const generateCode = () => {
    let code = getRandomInt(9999) + 10000;
    let url = `/api/v1/private/user/unlock_token/${user.id}/${code}`;

    getFromApi(url, user, (data) => {
      user.unlockToken = code;
      setCode(user.unlockToken);
      showToast("Code successfully generated", "success");
    }, (error) => {
      showToast(error, "error");
    });
  };

  let qrData = {
    action: 'addDevice',
    email: user.email,
    userId: user.id,
    token: `${user.unlockToken}`
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">Account</h1>
        <p className="text-muted-foreground mt-1">Manage your account settings and preferences</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Profile Information Card */}
        <Card className="border shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5 text-primary" />
              Profile Information
            </CardTitle>
            <CardDescription>Your personal and contact information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-center mb-6">
              <Avatar className="h-24 w-24">
                <AvatarImage src={user.photo} alt={user.name} />
                <AvatarFallback className="text-2xl">
                  {user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                </AvatarFallback>
              </Avatar>
            </div>

            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <User className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Name</p>
                  <p className="font-medium">{user.name || 'Not provided'}</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Mail className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Email</p>
                  <p className="font-medium">{user.email || 'Not provided'}</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Shield className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Username</p>
                  <p className="font-medium">{user.username || 'Not provided'}</p>
                </div>
              </div>
              
              <Separator className="my-3" />
              
              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Location</p>
                  <p className="font-medium">
                    {[user.city, user.province, user.country].filter(Boolean).join(', ') || 'Not provided'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Phone className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Phone</p>
                  <p className="font-medium">{user.phone || 'Not provided'}</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <Shield className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Roles</p>
                  <p className="font-medium">{user.roles || 'No roles assigned'}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* App Login Card */}
        <Card className="border shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5 text-primary" />
              App Login Code
            </CardTitle>
            <CardDescription>Scan this code to login on a mobile device</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Your Code</p>
                <p className="text-2xl font-bold">{user.unlockToken || 'No code generated'}</p>
              </div>
              <Button 
                onClick={generateCode} 
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Generate Code
              </Button>
            </div>
            
            {user.unlockToken ? (
              <>
                <div className="flex justify-center">
                  <div className="bg-white p-4 rounded-lg">
                    <QRCode value={JSON.stringify(qrData)} size={200} />
                  </div>
                </div>
                
                <div className="text-center text-sm text-muted-foreground">
                  <p>Scan to login to the app on a mobile device</p>
                  <p className="mt-1">You can only scan this code once. Generate a new code if you need to login again.</p>
                </div>
              </>
            ) : (
              <div className="text-center py-8 border rounded-md bg-muted/10">
                <div className="flex justify-center mb-4">
                  <Smartphone className="h-12 w-12 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-medium mb-2">No Code Generated</h3>
                <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                  Generate a code to create a QR code that you can scan with the mobile app for quick login.
                </p>
                <Button 
                  onClick={generateCode} 
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Generate Code
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
