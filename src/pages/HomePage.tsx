import React from 'react';
import { Button } from '@/components/ui/button';
import { FeatureCard } from '@/components/FeatureCard';
import { Users, Wrench, FileText, UserCheck, Package, Archive, PlayCircle } from 'lucide-react';

const features = [
  { icon: Users, title: 'Customer Management', description: 'Keep track of all your business and retail customers, service history, and contact details.', color: 'text-electric-blue' },
  { icon: Wrench, title: 'Work Orders', description: 'Create, assign, and track work orders for technicians with detailed status updates.', color: 'text-lime-green'},
  { icon: FileText, title: 'Invoicing', description: 'Generate and send professional invoices based on completed work orders and parts used.', color: 'text-bright-orange' },
  { icon: UserCheck, title: 'Team Management', description: 'Manage technician schedules, permissions, and performance.', color: 'text-soft-pink'},
  { icon: Package, title: 'Inventory Control', description: 'Track parts inventory, usage, stock levels, and supplier information.', color: 'text-deep-plum'},
  { icon: Archive, title: 'Bin Locations', description: 'Organize your inventory efficiently using bin locations for quick retrieval.', color: 'text-muted-foreground'}, // Example using default color
];

// Gemini home page

export function HomePage() {
  return (
    <div className="space-y-16 md:space-y-24">
      {/* Hero Section */}
      <section className="container mx-auto max-w-screen-lg px-4 md:px-6 pt-12 pb-8 text-center">
        <h1 className="text-4xl font-extrabold tracking-tight lg:text-5xl xl:text-6xl mb-4">
          Manage your service business easily.
        </h1>
        <p className="max-w-3xl mx-auto text-lg text-muted-foreground md:text-xl mb-8">
          OpenZcan helps you manage your service business more effectively. With AI-powered systems, OpenZcan delivers results faster than ever before.
        </p>
        <div className="flex flex-col sm:flex-row justify-center items-center gap-4 mb-12">
          <Button size="lg">Get Started Free</Button>
          <Button variant="outline" size="lg">
             <PlayCircle className="w-5 h-5 mr-2" />
             Watch Demo (Optional)
          </Button>
        </div>

        {/* Hero Graphic Placeholder */}
        <div className="relative mt-12 aspect-video max-w-3xl mx-auto bg-muted rounded-lg border border-border overflow-hidden flex items-center justify-center">
            {/* SVG Placeholder: Technician working */}
            <img src="/OZ-work-orders.png" alt="Technician working" />
           <span className="absolute bottom-2 right-2 text-[10px] text-muted-foreground">(SVG: Technician Graphic)</span>
           {/* Abstract shapes */}
           <div className="absolute -top-8 -left-8 w-20 h-20 bg-electric-blue/10 dark:bg-electric-blue/20 rounded-full blur-xl"></div>
           <div className="absolute -bottom-8 -right-8 w-24 h-24 bg-lime-green/10 dark:bg-lime-green/20 rounded-lg transform rotate-12 blur-lg"></div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="container mx-auto max-w-screen-lg px-4 md:px-6 py-16">
        <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">Core Features</h2>
            <p className="max-w-2xl mx-auto text-lg text-muted-foreground">Everything you need to streamline your service operations.</p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
            {features.map((feature) => (
            <FeatureCard
                key={feature.title}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                iconColorClass={feature.color} // Pass accent color class
            />
            ))}
        </div>
      </section>

       {/* Final CTA Section */}
       <section className="relative py-20 md:py-24 overflow-hidden bg-gradient-to-t from-muted/30 to-background dark:from-muted/10 dark:to-background">
             {/* Background SVG shapes */}
             <div className="absolute inset-0 -z-10 opacity-5 dark:opacity-10">
                <div className="absolute top-0 right-1/4 w-64 h-64 bg-bright-orange/30 rounded-full blur-3xl"></div>
                <div className="absolute bottom-1/4 left-10 w-80 h-80 bg-soft-pink/30 rounded-full blur-3xl"></div>
                <div className="absolute bottom-0 left-1/3 w-48 h-48 bg-deep-plum/20 rounded-xl blur-3xl"></div>
             </div>
             <div className="container mx-auto max-w-screen-md px-4 md:px-6 text-center">
                <h2 className="text-4xl font-extrabold tracking-tight lg:text-5xl mb-6">
                    Manage with clarity, service with confidence.
                </h2>
                <p className="max-w-xl mx-auto text-lg text-muted-foreground mb-10">
                   Take control of your workflow and empower your technicians with OpenZcan.
                </p>
                <Button size="lg">Start Your Free Trial</Button>
            </div>
        </section>
    </div>
  );
}