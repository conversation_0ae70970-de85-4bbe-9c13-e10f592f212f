import React, { useState, useEffect } from 'react';
import { 
  Package, Search, Plus, Filter, ArrowUpDown, MoreHorizontal, 
  Edit, Trash2, FileText, ArrowRight, Truck, Box, BarChart4
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, TableBody, TableCell, TableHead, 
  TableHeader, TableRow 
} from '@/components/ui/table';
import { 
  Card, CardContent, CardDescription, 
  CardHeader, CardTitle 
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

// Mock data for inventory items
const mockInventoryItems = [
  { 
    id: 1, 
    name: 'Copper Pipe 3/4"', 
    sku: 'CP-3475', 
    type: 'part', 
    category: 'plumbing',
    quantity: 45, 
    minQuantity: 10,
    cost: 12.99,
    price: 24.99,
    binId: 3,
    binName: 'Shelf A1',
    supplier: 'ABC Plumbing Supply',
    lastReceived: '2023-10-15',
  },
  { 
    id: 2, 
    name: 'PVC Elbow Joint 1"', 
    sku: 'PVC-EJ10', 
    type: 'part', 
    category: 'plumbing',
    quantity: 78, 
    minQuantity: 20,
    cost: 2.49,
    price: 5.99,
    binId: 3,
    binName: 'Shelf A1',
    supplier: 'ABC Plumbing Supply',
    lastReceived: '2023-11-02',
  },
  { 
    id: 3, 
    name: 'Pipe Wrench 12"', 
    sku: 'TL-PW12', 
    type: 'tool', 
    category: 'tools',
    quantity: 8, 
    minQuantity: 3,
    cost: 18.75,
    price: 32.99,
    binId: 4,
    binName: 'Toolbox #42',
    supplier: 'Tool World Inc.',
    lastReceived: '2023-09-28',
  },
  { 
    id: 4, 
    name: 'Teflon Tape', 
    sku: 'CN-TT50', 
    type: 'consumable', 
    category: 'plumbing',
    quantity: 120, 
    minQuantity: 30,
    cost: 0.99,
    price: 2.49,
    binId: 5,
    binName: 'Parts Bin B3',
    supplier: 'ABC Plumbing Supply',
    lastReceived: '2023-10-20',
  },
  { 
    id: 5, 
    name: 'Air Filter 16x20x1"', 
    sku: 'AF-16201', 
    type: 'part', 
    category: 'hvac',
    quantity: 25, 
    minQuantity: 10,
    cost: 8.50,
    price: 15.99,
    binId: 5,
    binName: 'Parts Bin B3',
    supplier: 'HVAC Wholesale',
    lastReceived: '2023-11-10',
  },
  { 
    id: 6, 
    name: 'Refrigerant R-410A', 
    sku: 'RF-410A', 
    type: 'consumable', 
    category: 'hvac',
    quantity: 5, 
    minQuantity: 2,
    cost: 85.00,
    price: 125.00,
    binId: 3,
    binName: 'Shelf A1',
    supplier: 'HVAC Wholesale',
    lastReceived: '2023-10-05',
  },
];

// Mock data for bins
const mockBins = [
  { id: 1, name: "Warehouse A", type: "warehouse", location: "Main Facility" },
  { id: 2, name: "Service Van #103", type: "vehicle", location: "Field" },
  { id: 3, name: "Shelf A1", type: "shelf", location: "Warehouse A" },
  { id: 4, name: "Toolbox #42", type: "container", location: "Service Van #103" },
  { id: 5, name: "Parts Bin B3", type: "bin", location: "Shelf A1" },
];

// Mock data for suppliers
const mockSuppliers = [
  { id: 1, name: "ABC Plumbing Supply", contact: "John Smith", phone: "************" },
  { id: 2, name: "HVAC Wholesale", contact: "Jane Doe", phone: "************" },
  { id: 3, name: "Tool World Inc.", contact: "Bob Johnson", phone: "************" },
];

// Mock data for inventory transactions
const mockTransactions = [
  { 
    id: 1, 
    type: 'received', 
    date: '2023-11-10', 
    itemId: 5, 
    itemName: 'Air Filter 16x20x1"',
    quantity: 15, 
    binId: 5,
    binName: 'Parts Bin B3',
    poNumber: 'PO-2023-089',
    user: 'Mike Johnson'
  },
  { 
    id: 2, 
    type: 'transferred', 
    date: '2023-11-08', 
    itemId: 3, 
    itemName: 'Pipe Wrench 12"',
    quantity: 2, 
    fromBinId: 3,
    fromBinName: 'Shelf A1',
    toBinId: 4,
    toBinName: 'Toolbox #42',
    user: 'Sarah Williams'
  },
  { 
    id: 3, 
    type: 'used', 
    date: '2023-11-05', 
    itemId: 4, 
    itemName: 'Teflon Tape',
    quantity: 5, 
    binId: 5,
    binName: 'Parts Bin B3',
    workOrderId: 'WO-2023-156',
    user: 'Mike Johnson'
  },
  { 
    id: 4, 
    type: 'received', 
    date: '2023-11-02', 
    itemId: 2, 
    itemName: 'PVC Elbow Joint 1"',
    quantity: 50, 
    binId: 3,
    binName: 'Shelf A1',
    poNumber: 'PO-2023-082',
    user: 'Sarah Williams'
  },
  { 
    id: 5, 
    type: 'adjusted', 
    date: '2023-10-28', 
    itemId: 1, 
    itemName: 'Copper Pipe 3/4"',
    quantity: -3, 
    binId: 3,
    binName: 'Shelf A1',
    reason: 'Inventory count correction',
    user: 'Admin'
  },
];

export function InventoryPage() {
  const [inventoryItems, setInventoryItems] = useState(mockInventoryItems);
  const [transactions, setTransactions] = useState(mockTransactions);
  const [bins, setBins] = useState(mockBins);
  const [suppliers, setSuppliers] = useState(mockSuppliers);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');
  const [isAddItemDialogOpen, setIsAddItemDialogOpen] = useState(false);
  const [isReceiveItemDialogOpen, setIsReceiveItemDialogOpen] = useState(false);
  const [isTransferItemDialogOpen, setIsTransferItemDialogOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  
  // New item form state
  const [newItem, setNewItem] = useState({
    name: '',
    sku: '',
    type: 'part',
    category: 'plumbing',
    quantity: 0,
    minQuantity: 0,
    cost: 0,
    price: 0,
    binId: '',
    supplier: '',
  });
  
  // Receive inventory form state
  const [receiveForm, setReceiveForm] = useState({
    itemId: '',
    quantity: '',
    binId: '',
    poNumber: '',
    date: new Date().toISOString().split('T')[0],
    notes: '',
  });
  
  // Transfer inventory form state
  const [transferForm, setTransferForm] = useState({
    itemId: '',
    quantity: '',
    fromBinId: '',
    toBinId: '',
    date: new Date().toISOString().split('T')[0],
    notes: '',
  });
  
  // Filter inventory items based on search query and active tab
  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = 
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.sku.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (activeTab === 'all') return matchesSearch;
    if (activeTab === 'parts') return matchesSearch && item.type === 'part';
    if (activeTab === 'consumables') return matchesSearch && item.type === 'consumable';
    if (activeTab === 'tools') return matchesSearch && item.type === 'tool';
    if (activeTab === 'low') return matchesSearch && item.quantity <= item.minQuantity;
    
    return matchesSearch;
  });
  
  // Handle adding a new inventory item
  const handleAddItem = () => {
    // Validate form
    if (!newItem.name || !newItem.sku || !newItem.binId) {
      console.error('Required fields missing');
      return;
    }
    
    // Create new item
    const item = {
      id: Date.now(),
      ...newItem,
      quantity: parseInt(newItem.quantity.toString(), 10),
      minQuantity: parseInt(newItem.minQuantity.toString(), 10),
      cost: parseFloat(newItem.cost.toString()),
      price: parseFloat(newItem.price.toString()),
      binName: bins.find(bin => bin.id.toString() === newItem.binId.toString())?.name || '',
      lastReceived: new Date().toISOString().split('T')[0],
    };
    
    // Add to inventory
    setInventoryItems([...inventoryItems, item]);
    
    // Add transaction
    const transaction = {
      id: Date.now(),
      type: 'received',
      date: new Date().toISOString().split('T')[0],
      itemId: item.id,
      itemName: item.name,
      quantity: item.quantity,
      binId: parseInt(newItem.binId.toString(), 10),
      binName: item.binName,
      poNumber: 'Initial',
      user: 'Current User',
    };
    
    setTransactions([transaction, ...transactions]);
    
    // Reset form and close dialog
    setNewItem({
      name: '',
      sku: '',
      type: 'part',
      category: 'plumbing',
      quantity: 0,
      minQuantity: 0,
      cost: 0,
      price: 0,
      binId: '',
      supplier: '',
    });
    setIsAddItemDialogOpen(false);
  };
  
  // Handle receiving inventory
  const handleReceiveInventory = () => {
    // Validate form
    if (!receiveForm.itemId || !receiveForm.quantity || !receiveForm.binId) {
      console.error('Required fields missing');
      return;
    }
    
    const itemId = parseInt(receiveForm.itemId.toString(), 10);
    const quantity = parseInt(receiveForm.quantity, 10);
    const binId = parseInt(receiveForm.binId.toString(), 10);
    
    // Update inventory quantity
    const updatedItems = inventoryItems.map(item => {
      if (item.id === itemId) {
        return {
          ...item,
          quantity: item.quantity + quantity,
          binId: binId,
          binName: bins.find(bin => bin.id === binId)?.name || item.binName,
          lastReceived: receiveForm.date,
        };
      }
      return item;
    });
    
    setInventoryItems(updatedItems);
    
    // Add transaction
    const item = inventoryItems.find(item => item.id === itemId);
    const transaction = {
      id: Date.now(),
      type: 'received',
      date: receiveForm.date,
      itemId: itemId,
      itemName: item?.name || '',
      quantity: quantity,
      binId: binId,
      binName: bins.find(bin => bin.id === binId)?.name || '',
      poNumber: receiveForm.poNumber || 'N/A',
      notes: receiveForm.notes,
      user: 'Current User',
    };
    
    setTransactions([transaction, ...transactions]);
    
    // Reset form and close dialog
    setReceiveForm({
      itemId: '',
      quantity: '',
      binId: '',
      poNumber: '',
      date: new Date().toISOString().split('T')[0],
      notes: '',
    });
    setIsReceiveItemDialogOpen(false);
  };
  
  // Handle transferring inventory
  const handleTransferInventory = () => {
    // Validate form
    if (!transferForm.itemId || !transferForm.quantity || !transferForm.fromBinId || !transferForm.toBinId) {
      console.error('Required fields missing');
      return;
    }
    
    const itemId = parseInt(transferForm.itemId.toString(), 10);
    const quantity = parseInt(transferForm.quantity, 10);
    const fromBinId = parseInt(transferForm.fromBinId.toString(), 10);
    const toBinId = parseInt(transferForm.toBinId.toString(), 10);
    
    // Check if item exists in the from bin
    const item = inventoryItems.find(item => item.id === itemId && item.binId === fromBinId);
    
    if (!item) {
      console.error('Item not found in the source bin');
      return;
    }
    
    if (item.quantity < quantity) {
      console.error('Not enough quantity available to transfer');
      return;
    }
    
    // Update inventory
    const updatedItems = inventoryItems.map(item => {
      if (item.id === itemId && item.binId === fromBinId) {
        return {
          ...item,
          quantity: item.quantity - quantity,
          binId: toBinId,
          binName: bins.find(bin => bin.id === toBinId)?.name || item.binName,
        };
      }
      return item;
    });
    
    setInventoryItems(updatedItems);
    
    // Add transaction
    const transaction = {
      id: Date.now(),
      type: 'transferred',
      date: transferForm.date,
      itemId: itemId,
      itemName: item?.name || '',
      quantity: quantity,
      fromBinId: fromBinId,
      fromBinName: bins.find(bin => bin.id === fromBinId)?.name || '',
      toBinId: toBinId,
      toBinName: bins.find(bin => bin.id === toBinId)?.name || '',
      notes: transferForm.notes,
      user: 'Current User',
    };
    
    setTransactions([transaction, ...transactions]);
    
    // Reset form and close dialog
    setTransferForm({
      itemId: '',
      quantity: '',
      fromBinId: '',
      toBinId: '',
      date: new Date().toISOString().split('T')[0],
      notes: '',
    });
    setIsTransferItemDialogOpen(false);
  };
  
  // Get bin name by ID
  const getBinName = (binId) => {
    const bin = bins.find(bin => bin.id === binId);
    return bin ? bin.name : 'Unknown';
  };
  
  // Get supplier name by ID
  const getSupplierName = (supplierId) => {
    const supplier = suppliers.find(supplier => supplier.id.toString() === supplierId.toString());
    return supplier ? supplier.name : 'Unknown';
  };
  
  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };
  
  return (
    <div className="space-y-6 p-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Inventory</h1>
          <p className="text-muted-foreground">Manage parts, consumables, and tools</p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={() => setIsReceiveItemDialogOpen(true)}
          >
            <Truck className="h-4 w-4" />
            Receive
          </Button>
          <Button 
            variant="outline" 
            className="flex items-center gap-2"
            onClick={() => setIsTransferItemDialogOpen(true)}
          >
            <ArrowRight className="h-4 w-4" />
            Transfer
          </Button>
          <Button 
            className="flex items-center gap-2"
            onClick={() => setIsAddItemDialogOpen(true)}
          >
            <Plus className="h-4 w-4" />
            Add Item
          </Button>
        </div>
      </div>
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 w-full max-w-sm">
          <Search className="h-4 w-4" />
          <Input
            type="search"
            placeholder="Search inventory items..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="!ring-0 focus:!ring-0"
          />
        </div>
        <div className="!ml-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="!bg-transparent !border-b-0">
              <TabsTrigger value="all" className="!bg-transparent !border-b-0">All</TabsTrigger>
              <TabsTrigger value="parts" className="!bg-transparent !border-b-0">Parts</TabsTrigger>
              <TabsTrigger value="consumables" className="!bg-transparent !border-b-0">Consumables</TabsTrigger>
              <TabsTrigger value="tools" className="!bg-transparent !border-b-0">Tools</TabsTrigger>
              <TabsTrigger value="low" className="!bg-transparent !border-b-0">Low Stock</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>
      
      <div className="!mt-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>SKU</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Min Quantity</TableHead>
              <TableHead>Cost</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Bin</TableHead>
              <TableHead>Supplier</TableHead>
              <TableHead>Last Received</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredItems.map(item => (
              <TableRow key={item.id}>
                <TableCell>{item.name}</TableCell>
                <TableCell>{item.sku}</TableCell>
                <TableCell>{item.type}</TableCell>
                <TableCell>{item.category}</TableCell>
                <TableCell>
                  <Badge 
                    variant={item.quantity <= item.minQuantity ? "destructive" : "default"}
                  >
                    {item.quantity}
                  </Badge>
                </TableCell>
                <TableCell>{item.minQuantity}</TableCell>
                <TableCell>{formatCurrency(item.cost)}</TableCell>
                <TableCell>{formatCurrency(item.price)}</TableCell>
                <TableCell>{getBinName(item.binId)}</TableCell>
                <TableCell>{getSupplierName(item.supplier)}</TableCell>
                <TableCell>{item.lastReceived}</TableCell>
                <TableCell className="!text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="!p-0">
                        <MoreHorizontal className="!h-4 !w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="!w-48">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Edit className="!h-4 !w-4 !mr-2" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Truck className="!h-4 !w-4 !mr-2" />
                        Receive
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <ArrowRight className="!h-4 !w-4 !mr-2" />
                        Transfer
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Trash2 className="!h-4 !w-4 !mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      <div className="!mt-8">
        <h2 className="text-xl font-bold mb-4">Recent Transactions</h2>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Item</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Reference</TableHead>
              <TableHead>User</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transactions.slice(0, 5).map(transaction => (
              <TableRow key={transaction.id}>
                <TableCell>{transaction.date}</TableCell>
                <TableCell>
                  <Badge 
                    variant={
                      transaction.type === 'received' ? 'default' : 
                      transaction.type === 'transferred' ? 'secondary' : 
                      transaction.type === 'used' ? 'outline' : 'destructive'
                    }
                  >
                    {transaction.type}
                  </Badge>
                </TableCell>
                <TableCell>{transaction.itemName}</TableCell>
                <TableCell>{transaction.quantity}</TableCell>
                <TableCell>
                  {transaction.type === 'transferred' 
                    ? `${transaction.fromBinName} → ${transaction.toBinName}`
                    : transaction.binName
                  }
                </TableCell>
                <TableCell>
                  {transaction.poNumber || transaction.workOrderId || transaction.reason || '-'}
                </TableCell>
                <TableCell>{transaction.user}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      
      {/* Add Item Dialog */}
      <Dialog open={isAddItemDialogOpen} onOpenChange={setIsAddItemDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add Inventory Item</DialogTitle>
            <DialogDescription>
              Add a new part, consumable, or tool to your inventory.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={newItem.name}
                  onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                  placeholder="Item name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sku">SKU</Label>
                <Input
                  id="sku"
                  value={newItem.sku}
                  onChange={(e) => setNewItem({ ...newItem, sku: e.target.value })}
                  placeholder="Stock keeping unit"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Type</Label>
                <Select
                  value={newItem.type}
                  onValueChange={(value) => setNewItem({ ...newItem, type: value })}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="part">Part</SelectItem>
                    <SelectItem value="consumable">Consumable</SelectItem>
                    <SelectItem value="tool">Tool</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={newItem.category}
                  onValueChange={(value) => setNewItem({ ...newItem, category: value })}
                >
                  <SelectTrigger id="category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="plumbing">Plumbing</SelectItem>
                    <SelectItem value="electrical">Electrical</SelectItem>
                    <SelectItem value="hvac">HVAC</SelectItem>
                    <SelectItem value="tools">Tools</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quantity">Initial Quantity</Label>
                <Input
                  id="quantity"
                  type="number"
                  value={newItem.quantity}
                  onChange={(e) => setNewItem({ ...newItem, quantity: e.target.value })}
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="minQuantity">Min Quantity</Label>
                <Input
                  id="minQuantity"
                  type="number"
                  value={newItem.minQuantity}
                  onChange={(e) => setNewItem({ ...newItem, minQuantity: e.target.value })}
                  min="0"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="cost">Cost</Label>
                <Input
                  id="cost"
                  type="number"
                  value={newItem.cost}
                  onChange={(e) => setNewItem({ ...newItem, cost: e.target.value })}
                  min="0"
                  step="0.01"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="price">Price</Label>
                <Input
                  id="price"
                  type="number"
                  value={newItem.price}
                  onChange={(e) => setNewItem({ ...newItem, price: e.target.value })}
                  min="0"
                  step="0.01"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="binId">Bin Location</Label>
                <Select
                  value={newItem.binId.toString()}
                  onValueChange={(value) => setNewItem({ ...newItem, binId: value })}
                >
                  <SelectTrigger id="binId">
                    <SelectValue placeholder="Select bin" />
                  </SelectTrigger>
                  <SelectContent>
                    {bins.map(bin => (
                      <SelectItem key={bin.id} value={bin.id.toString()}>
                        {bin.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="supplier">Supplier</Label>
                <Select
                  value={newItem.supplier.toString()}
                  onValueChange={(value) => setNewItem({ ...newItem, supplier: value })}
                >
                  <SelectTrigger id="supplier">
                    <SelectValue placeholder="Select supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map(supplier => (
                      <SelectItem key={supplier.id} value={supplier.id.toString()}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddItemDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddItem}>Add Item</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Receive Inventory Dialog */}
      <Dialog open={isReceiveItemDialogOpen} onOpenChange={setIsReceiveItemDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Receive Inventory</DialogTitle>
            <DialogDescription>
              Record inventory received from suppliers.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="receiveItemId">Item</Label>
              <Select
                value={receiveForm.itemId.toString()}
                onValueChange={(value) => setReceiveForm({ ...receiveForm, itemId: value })}
              >
                <SelectTrigger id="receiveItemId">
                  <SelectValue placeholder="Select item" />
                </SelectTrigger>
                <SelectContent>
                  {inventoryItems.map(item => (
                    <SelectItem key={item.id} value={item.id.toString()}>
                      {item.name} ({item.sku})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="receiveQuantity">Quantity</Label>
                <Input
                  id="receiveQuantity"
                  type="number"
                  value={receiveForm.quantity}
                  onChange={(e) => setReceiveForm({ ...receiveForm, quantity: e.target.value })}
                  min="1"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="receiveBinId">Bin Location</Label>
                <Select
                  value={receiveForm.binId.toString()}
                  onValueChange={(value) => setReceiveForm({ ...receiveForm, binId: value })}
                >
                  <SelectTrigger id="receiveBinId">
                    <SelectValue placeholder="Select bin" />
                  </SelectTrigger>
                  <SelectContent>
                    {bins.map(bin => (
                      <SelectItem key={bin.id} value={bin.id.toString()}>
                        {bin.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="receiveDate">Date</Label>
                <Input
                  id="receiveDate"
                  type="date"
                  value={receiveForm.date}
                  onChange={(e) => setReceiveForm({ ...receiveForm, date: e.target.value })}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="receivePO">PO Number</Label>
                <Input
                  id="receivePO"
                  value={receiveForm.poNumber}
                  onChange={(e) => setReceiveForm({ ...receiveForm, poNumber: e.target.value })}
                  placeholder="Purchase order #"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="receiveNotes">Notes</Label>
              <Textarea
                id="receiveNotes"
                value={receiveForm.notes}
                onChange={(e) => setReceiveForm({ ...receiveForm, notes: e.target.value })}
                placeholder="Additional details"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReceiveItemDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleReceiveInventory}>Receive</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Transfer Inventory Dialog */}
      <Dialog open={isTransferItemDialogOpen} onOpenChange={setIsTransferItemDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Transfer Inventory</DialogTitle>
            <DialogDescription>
              Move inventory between bins or locations.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="transferItemId">Item</Label>
              <Select
                value={transferForm.itemId.toString()}
                onValueChange={(value) => setTransferForm({ ...transferForm, itemId: value })}
              >
                <SelectTrigger id="transferItemId">
                  <SelectValue placeholder="Select item" />
                </SelectTrigger>
                <SelectContent>
                  {inventoryItems.map(item => (
                    <SelectItem key={item.id} value={item.id.toString()}>
                      {item.name} ({item.sku}) - {getBinName(item.binId)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="transferQuantity">Quantity</Label>
              <Input
                id="transferQuantity"
                type="number"
                value={transferForm.quantity}
                onChange={(e) => setTransferForm({ ...transferForm, quantity: e.target.value })}
                min="1"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="transferFromBin">From Bin</Label>
                <Select
                  value={transferForm.fromBinId.toString()}
                  onValueChange={(value) => setTransferForm({ ...transferForm, fromBinId: value })}
                >
                  <SelectTrigger id="transferFromBin">
                    <SelectValue placeholder="Select source" />
                  </SelectTrigger>
                  <SelectContent>
                    {bins.map(bin => (
                      <SelectItem key={bin.id} value={bin.id.toString()}>
                        {bin.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="transferToBin">To Bin</Label>
                <Select
                  value={transferForm.toBinId.toString()}
                  onValueChange={(value) => setTransferForm({ ...transferForm, toBinId: value })}
                >
                  <SelectTrigger id="transferToBin">
                    <SelectValue placeholder="Select destination" />
                  </SelectTrigger>
                  <SelectContent>
                    {bins.map(bin => (
                      <SelectItem key={bin.id} value={bin.id.toString()}>
                        {bin.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="transferDate">Date</Label>
              <Input
                id="transferDate"
                type="date"
                value={transferForm.date}
                onChange={(e) => setTransferForm({ ...transferForm, date: e.target.value })}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="transferNotes">Notes</Label>
              <Textarea
                id="transferNotes"
                value={transferForm.notes}
                onChange={(e) => setTransferForm({ ...transferForm, notes: e.target.value })}
                placeholder="Reason for transfer"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsTransferItemDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleTransferInventory}>Transfer</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
