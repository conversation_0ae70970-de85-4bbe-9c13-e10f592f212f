import { useState, useContext } from "react";
import { AlertCircle, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ConfigContext } from "@/context/ConfigContext";
import { BusinessContext } from "@/context/BusinessContext";
import {
  ServiceOperationsSection,
  FinancialMetricsSection,
  VehicleCostSection
} from "@/components/dashboard";
import { useDashboardData } from "@/hooks/useDashboardData";
import { IProviderRes, ISessionUser } from "@/types";

export function Dashboard() {
  const { user }: { user: ISessionUser } = useContext(ConfigContext);
  const { provider }: { provider: IProviderRes } = useContext(BusinessContext);
  const [timeFilter, setTimeFilter] = useState("30d");

  const { metrics, loading, error, refetch } = useDashboardData(timeFilter);

  if (!user || !provider) {
    return (
      <main className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="container mx-auto p-6 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground"></p>
        </div>
      </div>
      {/* Error State */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to load dashboard data: {error}</span>
            <Button variant="outline" size="sm" onClick={refetch}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Service & Operations Section */}
      <ServiceOperationsSection
        metrics={metrics}
        timeFilter={timeFilter}
        onTimeFilterChange={setTimeFilter}
        loading={loading}
      />

      {/* Financial Metrics Section */}
      <FinancialMetricsSection
        metrics={metrics}
        timeFilter={timeFilter}
        onTimeFilterChange={setTimeFilter}
        loading={loading}
      />

      {/* Vehicle Cost Tracking Section */}
      <VehicleCostSection
        timeFilter={timeFilter}
        onTimeFilterChange={setTimeFilter}
        loading={loading}
      />
    </main>
  );
}