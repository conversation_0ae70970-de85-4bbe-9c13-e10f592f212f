import React, { useState, useEffect } from 'react';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, Search, ArrowLeft, Package, Edit, Trash2, Truck, Fuel, DollarSign, MapPin, Wrench } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

import MapSelector from "@/components/map_selector";

const L = window.L;

// Fix Leaflet icon issue
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
});

// Mock data for vehicles
const mockVehicles = [
  {
    id: 1,
    name: "Service Van #103",
    type: "Van",
    model: "Ford Transit",
    year: 2021,
    licensePlate: "ABC-1234",
    status: "active",
    fuelType: "Diesel",
    currentLocation: [40.7128, -74.0060], // NYC coordinates
    lastUpdated: "2023-06-15T14:30:00Z",
    assignedTo: "John Doe",
    mileage: 45000,
    nextService: "2023-07-15"
  },
  {
    id: 2,
    name: "Pickup Truck #205",
    type: "Truck",
    model: "Toyota Tundra",
    year: 2020,
    licensePlate: "XYZ-5678",
    status: "maintenance",
    fuelType: "Gasoline",
    currentLocation: [34.0522, -118.2437], // LA coordinates
    lastUpdated: "2023-06-14T09:15:00Z",
    assignedTo: "Jane Smith",
    mileage: 32000,
    nextService: "2023-08-01"
  },
];

// Mock data for expenses
const mockExpenses = [
  { id: 1, vehicleId: 1, date: "2023-06-10", type: "Fuel", amount: 75.50, notes: "Full tank", odometer: 44500 },
  { id: 2, vehicleId: 1, date: "2023-06-05", type: "Maintenance", amount: 350.00, notes: "Oil change and filter", odometer: 44000 },
  { id: 3, vehicleId: 1, date: "2023-05-28", type: "Repair", amount: 220.00, notes: "Replace brake pads", odometer: 43500 },
  { id: 4, vehicleId: 2, date: "2023-06-12", type: "Fuel", amount: 85.75, notes: "Full tank", odometer: 31800 },
  { id: 5, vehicleId: 2, date: "2023-06-01", type: "Toll", amount: 15.00, notes: "Highway toll", odometer: 31500 },
];

// Mock data for inventory on vehicles
const mockVehicleInventory = [
  { id: 1, vehicleId: 1, name: "Pipe Wrench", sku: "TL-001", quantity: 3 },
  { id: 2, vehicleId: 1, name: "Copper Fittings", sku: "PL-101", quantity: 25 },
  { id: 3, vehicleId: 1, name: "PVC Pipe 2\"", sku: "PL-202", quantity: 10 },
  { id: 4, vehicleId: 2, name: "Hammer", sku: "TL-005", quantity: 2 },
  { id: 5, vehicleId: 2, name: "Electrical Tape", sku: "EL-110", quantity: 8 },
];

export function VehiclesPage() {
  const [vehicles, setVehicles] = useState(mockVehicles);
  const [expenses, setExpenses] = useState(mockExpenses);
  const [inventory, setInventory] = useState(mockVehicleInventory);
  const [selectedVehicle, setSelectedVehicle] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isAddVehicleDialogOpen, setIsAddVehicleDialogOpen] = useState(false);
  const [isAddExpenseDialogOpen, setIsAddExpenseDialogOpen] = useState(false);
  const [isAddInventoryDialogOpen, setIsAddInventoryDialogOpen] = useState(false);
  const [newVehicle, setNewVehicle] = useState({
    name: "",
    type: "Van",
    model: "",
    year: new Date().getFullYear(),
    licensePlate: "",
    fuelType: "Gasoline"
  });
  const [newExpense, setNewExpense] = useState({
    date: new Date().toISOString().split('T')[0],
    type: "Fuel",
    amount: "",
    notes: "",
    odometer: ""
  });
  const [newInventoryItem, setNewInventoryItem] = useState({
    name: "",
    sku: "",
    quantity: "1"
  });
  const [editingItem, setEditingItem] = useState(null);
  const [editValue, setEditValue] = useState("");
  const [isChanged, setIsChanged] = useState(false);

  // Add this function to update inventory quantity
  const updateInventoryQuantity = (itemId, newQuantity) => {
    // In a real app, this would be an API call
    console.log(`Updating item ${itemId} to quantity ${newQuantity}`);

    // Update local state
    const updatedInventory = inventory.map(item =>
      item.id === itemId ? { ...item, quantity: parseInt(newQuantity, 10) } : item
    );
    setInventory(updatedInventory);
    setEditingItem(null);
    setIsChanged(false);
  };

  // Handle keydown for quantity input
  const handleQuantityKeyDown = (e, itemId) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      updateInventoryQuantity(itemId, editValue);
    } else if (e.key === 'Escape') {
      setEditingItem(null);
      setIsChanged(false);
    }
  };

  // Handle quantity change
  const handleQuantityChange = (e) => {
    const value = e.target.value;
    // Only allow numbers
    if (/^\d*$/.test(value)) {
      setEditValue(value);
      setIsChanged(true);
    }
  };

  // Start editing an item
  const startEditing = (item) => {
    setEditingItem(item.id);
    setEditValue(item.quantity.toString());
    setIsChanged(false);
  };

  // Filter vehicles based on search query
  const filteredVehicles = vehicles.filter(vehicle =>
    vehicle.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    vehicle.model.toLowerCase().includes(searchQuery.toLowerCase()) ||
    vehicle.licensePlate.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get expenses for selected vehicle
  const getVehicleExpenses = (vehicleId) => {
    return expenses.filter(expense => expense.vehicleId === vehicleId);
  };

  // Get inventory for selected vehicle
  const getVehicleInventory = (vehicleId) => {
    return inventory.filter(item => item.vehicleId === vehicleId);
  };

  // Handle vehicle selection
  const handleVehicleClick = (vehicle) => {
    setSelectedVehicle(vehicle);
  };

  // Handle back button click
  const handleBackClick = () => {
    setSelectedVehicle(null);
  };

  // Handle adding a new vehicle
  const handleAddVehicle = () => {
    if (!newVehicle.name || !newVehicle.model || !newVehicle.licensePlate) {
      console.error("Required fields missing");
      return;
    }

    const vehicle = {
      id: Date.now(),
      ...newVehicle,
      status: "active",
      currentLocation: [40.7128, -74.0060], // Default to NYC
      lastUpdated: new Date().toISOString(),
      mileage: 0,
      nextService: new Date(new Date().setMonth(new Date().getMonth() + 3)).toISOString().split('T')[0]
    };

    setVehicles([...vehicles, vehicle]);
    setNewVehicle({
      name: "",
      type: "Van",
      model: "",
      year: new Date().getFullYear(),
      licensePlate: "",
      fuelType: "Gasoline"
    });
    setIsAddVehicleDialogOpen(false);
  };

  // Handle adding a new expense
  const handleAddExpense = () => {
    if (!newExpense.amount || !newExpense.odometer) {
      console.error("Required fields missing");
      return;
    }

    const expense = {
      id: Date.now(),
      vehicleId: selectedVehicle.id,
      ...newExpense,
      amount: parseFloat(newExpense.amount),
      odometer: parseInt(newExpense.odometer, 10)
    };

    setExpenses([...expenses, expense]);
    setNewExpense({
      date: new Date().toISOString().split('T')[0],
      type: "Fuel",
      amount: "",
      notes: "",
      odometer: ""
    });
    setIsAddExpenseDialogOpen(false);
  };

  // Handle adding a new inventory item
  const handleAddInventoryItem = () => {
    if (!newInventoryItem.name || !newInventoryItem.sku) {
      console.error("Required fields missing");
      return;
    }

    const item = {
      id: Date.now(),
      vehicleId: selectedVehicle.id,
      ...newInventoryItem,
      quantity: parseInt(newInventoryItem.quantity, 10)
    };

    setInventory([...inventory, item]);
    setNewInventoryItem({
      name: "",
      sku: "",
      quantity: "1"
    });
    setIsAddInventoryDialogOpen(false);
  };

  // Render vehicle details view
  const VehicleDetails = () => {
    const vehicleExpenses = getVehicleExpenses(selectedVehicle.id);
    const vehicleInventory = getVehicleInventory(selectedVehicle.id);

    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={handleBackClick}
            className="h-8 w-8"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="flex-1">
            <h1 className="text-3xl font-bold">{selectedVehicle.name}</h1>
            <p className="text-muted-foreground">
              {selectedVehicle.year} {selectedVehicle.model} • {selectedVehicle.licensePlate}
            </p>
          </div>
          <Badge
            className={selectedVehicle.status === "active" ? "bg-green-500" : "bg-yellow-500"}
          >
            {selectedVehicle.status.charAt(0).toUpperCase() + selectedVehicle.status.slice(1)}
          </Badge>
        </div>

        <Tabs defaultValue="details">
          <TabsList>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="location">Location</TabsTrigger>
            <TabsTrigger value="expenses">Expenses</TabsTrigger>
            <TabsTrigger value="inventory">Inventory</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="mt-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-medium mb-4">Vehicle Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Vehicle Name</p>
                    <p>{selectedVehicle.name}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Type</p>
                    <p>{selectedVehicle.type}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Model</p>
                    <p>{selectedVehicle.model}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Year</p>
                    <p>{selectedVehicle.year}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">License Plate</p>
                    <p>{selectedVehicle.licensePlate}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Fuel Type</p>
                    <p>{selectedVehicle.fuelType}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Current Mileage</p>
                    <p>{selectedVehicle.mileage.toLocaleString()} miles</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Next Service Date</p>
                    <p>{new Date(selectedVehicle.nextService).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Assigned To</p>
                    <p>{selectedVehicle.assignedTo || "Unassigned"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Last Updated</p>
                    <p>{new Date(selectedVehicle.lastUpdated).toLocaleString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="location" className="mt-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-lg font-medium mb-4">Vehicle Location</h3>
                <div className="h-[400px] w-full rounded-md overflow-hidden">
                  {/*   <MapContainer 
                    center={selectedVehicle.currentLocation} 
                    zoom={13} 
                    style={{ height: '100%', width: '100%' }}
                  >
                    <TileLayer
                      url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                    />
                    <Marker position={selectedVehicle.currentLocation}>
                      <Popup>
                        {selectedVehicle.name}<br/>
                        Last updated: {new Date(selectedVehicle.lastUpdated).toLocaleString()}
                      </Popup>
                    </Marker>
                  </MapContainer> */}


                  <div className="mb-2">
                    <MapSelector title={"Location"}
                      position={selectedVehicle.currentLocation}
                      location={location} onMapClick={(e) => {
                        //console.log("map clicked", typeof e, e)
                        //setLatlng(e)
                      }} />
                  </div>

                </div>
                <p className="text-sm text-muted-foreground mt-4">
                  Last location update: {new Date(selectedVehicle.lastUpdated).toLocaleString()}
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="expenses" className="mt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Expense History</h3>
              <Button
                size="sm"
                className="flex items-center gap-2"
                onClick={() => setIsAddExpenseDialogOpen(true)}
              >
                <Plus className="h-4 w-4" />
                Add Expense
              </Button>
            </div>

            {vehicleExpenses.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Odometer</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {vehicleExpenses.map((expense) => (
                    <TableRow key={expense.id}>
                      <TableCell>{new Date(expense.date).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {expense.type === "Fuel" ? <Fuel className="h-3 w-3 mr-1 inline" /> : null}
                          {expense.type === "Maintenance" ? <Wrench className="h-3 w-3 mr-1 inline" /> : null}
                          {expense.type === "Repair" ? <Wrench className="h-3 w-3 mr-1 inline" /> : null}
                          {expense.type === "Toll" ? <DollarSign className="h-3 w-3 mr-1 inline" /> : null}
                          {expense.type}
                        </Badge>
                      </TableCell>
                      <TableCell>${expense.amount.toFixed(2)}</TableCell>
                      <TableCell>{expense.odometer.toLocaleString()} miles</TableCell>
                      <TableCell>{expense.notes}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-6 border rounded-md bg-muted/10">
                <p className="text-muted-foreground mb-2">No expenses recorded for this vehicle.</p>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2 mx-auto"
                  onClick={() => setIsAddExpenseDialogOpen(true)}
                >
                  <Plus className="h-4 w-4" />
                  Add First Expense
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="inventory" className="mt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Vehicle Inventory</h3>
              <Button
                size="sm"
                className="flex items-center gap-2"
                onClick={() => setIsAddInventoryDialogOpen(true)}
              >
                <Plus className="h-4 w-4" />
                Add Item
              </Button>
            </div>

            {vehicleInventory.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item Name</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {vehicleInventory.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell>{item.sku}</TableCell>
                      <TableCell>
                        {editingItem === item.id ? (
                          <Input
                            type="text"
                            value={editValue}
                            onChange={handleQuantityChange}
                            onKeyDown={(e) => handleQuantityKeyDown(e, item.id)}
                            autoFocus
                            className={`w-20 h-8 ${isChanged ? 'border-red-500' : ''}`}
                            onBlur={() => {
                              if (isChanged) {
                                updateInventoryQuantity(item.id, editValue);
                              } else {
                                setEditingItem(null);
                              }
                            }}
                          />
                        ) : (
                          <span
                            className="cursor-pointer hover:underline"
                            onClick={() => startEditing(item)}
                          >
                            {item.quantity}
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => startEditing(item)}
                          >
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive">
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-6 border rounded-md bg-muted/10">
                <div className="flex justify-center mb-2">
                  <Package className="h-10 w-10 text-muted-foreground" />
                </div>
                <p className="text-muted-foreground mb-2">No inventory items found in this vehicle.</p>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2 mx-auto"
                  onClick={() => setIsAddInventoryDialogOpen(true)}
                >
                  <Plus className="h-4 w-4" />
                  Add First Item
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Add Expense Dialog */}
        <Dialog open={isAddExpenseDialogOpen} onOpenChange={setIsAddExpenseDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add Expense</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expenseDate" className="text-right">
                  Date
                </Label>
                <Input
                  id="expenseDate"
                  name="date"
                  type="date"
                  value={newExpense.date}
                  onChange={(e) => setNewExpense({ ...newExpense, date: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expenseType" className="text-right">
                  Type
                </Label>
                <Select
                  value={newExpense.type}
                  onValueChange={(value) => setNewExpense({ ...newExpense, type: value })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select expense type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Fuel">Fuel</SelectItem>
                    <SelectItem value="Maintenance">Maintenance</SelectItem>
                    <SelectItem value="Repair">Repair</SelectItem>
                    <SelectItem value="Toll">Toll</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expenseAmount" className="text-right">
                  Amount
                </Label>
                <Input
                  id="expenseAmount"
                  name="amount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={newExpense.amount}
                  onChange={(e) => setNewExpense({ ...newExpense, amount: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expenseOdometer" className="text-right">
                  Odometer
                </Label>
                <Input
                  id="expenseOdometer"
                  name="odometer"
                  type="number"
                  placeholder="Current mileage"
                  value={newExpense.odometer}
                  onChange={(e) => setNewExpense({ ...newExpense, odometer: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expenseNotes" className="text-right">
                  Notes
                </Label>
                <Input
                  id="expenseNotes"
                  name="notes"
                  placeholder="Optional notes"
                  value={newExpense.notes}
                  onChange={(e) => setNewExpense({ ...newExpense, notes: e.target.value })}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleAddExpense}>Add Expense</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Add Inventory Item Dialog */}
        <Dialog open={isAddInventoryDialogOpen} onOpenChange={setIsAddInventoryDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add Inventory Item</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="itemName" className="text-right">
                  Name
                </Label>
                <Input
                  id="itemName"
                  name="name"
                  value={newInventoryItem.name}
                  onChange={(e) => setNewInventoryItem({ ...newInventoryItem, name: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="itemSku" className="text-right">
                  SKU
                </Label>
                <Input
                  id="itemSku"
                  name="sku"
                  value={newInventoryItem.sku}
                  onChange={(e) => setNewInventoryItem({ ...newInventoryItem, sku: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="itemQuantity" className="text-right">
                  Quantity
                </Label>
                <Input
                  id="itemQuantity"
                  name="quantity"
                  type="number"
                  value={newInventoryItem.quantity}
                  onChange={(e) => setNewInventoryItem({ ...newInventoryItem, quantity: e.target.value })}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleAddInventoryItem}>Add Item</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  };

  // Main vehicles list view
  const VehiclesList = () => {
    return (
      <div className="space-y-6 p-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Vehicles</h1>
            <p className="text-muted-foreground">Manage your fleet, track expenses, and monitor inventory.</p>
          </div>
          <Button
            className="flex items-center gap-2"
            onClick={() => setIsAddVehicleDialogOpen(true)}
          >
            <Plus className="h-4 w-4" />
            Add Vehicle
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search vehicles..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredVehicles.map((vehicle) => (
            <Card
              key={vehicle.id}
              className="cursor-pointer transition-all hover:shadow-md"
              onClick={() => handleVehicleClick(vehicle)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Truck className="h-5 w-5 text-muted-foreground" />
                    <h3 className="font-medium">{vehicle.name}</h3>
                  </div>
                  <Badge
                    className={vehicle.status === "active" ? "bg-green-500" : "bg-yellow-500"}
                  >
                    {vehicle.status.charAt(0).toUpperCase() + vehicle.status.slice(1)}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-2">
                  {vehicle.year} {vehicle.model} • {vehicle.licensePlate}
                </p>
                <div className="flex justify-between text-sm mt-4">
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <span>Last updated: {new Date(vehicle.lastUpdated).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Fuel className="h-4 w-4 text-muted-foreground" />
                    <span>{vehicle.fuelType}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredVehicles.length === 0 && (
          <div className="text-center py-12 border rounded-md bg-muted/10">
            <div className="flex justify-center mb-4">
              <Truck className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">No Vehicles Found</h3>
            <p className="text-muted-foreground mb-4">
              {searchQuery ? "No vehicles match your search criteria." : "Add your first vehicle to start tracking your fleet."}
            </p>
            <Button
              className="flex items-center gap-2"
              onClick={() => setIsAddVehicleDialogOpen(true)}
            >
              <Plus className="h-4 w-4" />
              Add First Vehicle
            </Button>
          </div>
        )}

        {/* Add Vehicle Dialog */}
        <Dialog open={isAddVehicleDialogOpen} onOpenChange={setIsAddVehicleDialogOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add New Vehicle</DialogTitle>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="vehicleName" className="text-right">
                  Name
                </Label>
                <Input
                  id="vehicleName"
                  name="name"
                  value={newVehicle.name}
                  onChange={(e) => setNewVehicle({ ...newVehicle, name: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="vehicleType" className="text-right">
                  Type
                </Label>
                <Select
                  value={newVehicle.type}
                  onValueChange={(value) => setNewVehicle({ ...newVehicle, type: value })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select vehicle type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Van">Van</SelectItem>
                    <SelectItem value="Truck">Truck</SelectItem>
                    <SelectItem value="Car">Car</SelectItem>
                    <SelectItem value="SUV">SUV</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="vehicleModel" className="text-right">
                  Model
                </Label>
                <Input
                  id="vehicleModel"
                  name="model"
                  value={newVehicle.model}
                  onChange={(e) => setNewVehicle({ ...newVehicle, model: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="vehicleYear" className="text-right">
                  Year
                </Label>
                <Input
                  id="vehicleYear"
                  name="year"
                  type="number"
                  value={newVehicle.year}
                  onChange={(e) => setNewVehicle({ ...newVehicle, year: parseInt(e.target.value, 10) })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="vehicleLicensePlate" className="text-right">
                  License Plate
                </Label>
                <Input
                  id="vehicleLicensePlate"
                  name="licensePlate"
                  value={newVehicle.licensePlate}
                  onChange={(e) => setNewVehicle({ ...newVehicle, licensePlate: e.target.value })}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="vehicleFuelType" className="text-right">
                  Fuel Type
                </Label>
                <Select
                  value={newVehicle.fuelType}
                  onValueChange={(value) => setNewVehicle({ ...newVehicle, fuelType: value })}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select fuel type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Gasoline">Gasoline</SelectItem>
                    <SelectItem value="Diesel">Diesel</SelectItem>
                    <SelectItem value="Electric">Electric</SelectItem>
                    <SelectItem value="Hybrid">Hybrid</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" onClick={handleAddVehicle}>Add Vehicle</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  };

  // Render the appropriate view based on whether a vehicle is selected
  return selectedVehicle ? <VehicleDetails /> : <VehiclesList />;
}
