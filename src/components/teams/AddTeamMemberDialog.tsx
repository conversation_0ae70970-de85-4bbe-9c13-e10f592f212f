import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { SearchUsersBar } from "./SearchUserBar";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";

/**
 * Renders a dialog for adding team members.
 *
 * @param {boolean} isOpen - Flag to control the visibility of the dialog.
 * @param {function} setIsOpen - Function to set the state of the dialog visibility.
 * @param {function} onUserSelected - Function to handle when a user is selected.
 * @return {JSX.Element} The dialog component for adding team members.
 */
interface AddTeamMemberDialogProps {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  onUserSelected: (user: any) => void;
  selectedLocation: any;
}
type MemberType = "Partner" | "Administrator" | "Operator" | "Manager" | "Owner" | "Contractor" | "Technician";

export function AddTeamMemberDialog({ isOpen, setIsOpen, onUserSelected, selectedLocation }: AddTeamMemberDialogProps) {

  const [selectedUser, setSelectedUser] = useState<any | null>(null);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [memberType, setMemberType] = useState<MemberType | "">("")

  const handleSelectedUser = (user) => {
    console.log(user)
    setSelectedUser(user);
    setConfirmOpen(true);
    setMemberType("");
  }

  const confirmSelection = () => {
    if (selectedUser) {
      if (selectedUser && memberType) {
        onUserSelected({
          ...selectedUser,
          type: memberType,
        });
        setSelectedUser(null);
        setConfirmOpen(false);
        setIsOpen(false);
      }
    }
  };

  const cancelSelection = () => {
    setSelectedUser(null);
    setConfirmOpen(false);
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Add Team Members</DialogTitle>
            <p className="text-sm text-muted-foreground">
              Search and select users to add to your team.
            </p>
          </DialogHeader>
          <div className="overflow-y-auto max-h-[60vh] pr-2">
            <SearchUsersBar onUserSelected={handleSelectedUser} />
          </div>
        </DialogContent>
      </Dialog>
      {/* Confirmation dialog */}
      <Dialog open={confirmOpen} onOpenChange={setConfirmOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm User Selection</DialogTitle>
            <p className="text-sm text-muted-foreground">
              Please confirm that you want to add this user to the team.
            </p>
          </DialogHeader>

          {/* User Info Section */}
          {selectedUser && (
            <div className="bg-muted/40 rounded-md p-4 text-sm space-y-2 border">
              <div>
                <span className="font-medium">Name:</span> {selectedUser.name}
              </div>
              <div>
                <span className="font-medium">Email:</span> {selectedUser.email}
              </div>
              {selectedUser.username && (
                <div>
                  <span className="font-medium">Username:</span> {selectedUser.username}
                </div>
              )}
              {selectedUser.phone && (
                <div>
                  <span className="font-medium">Phone:</span> {selectedUser.phone}
                </div>
              )}
              {(selectedUser.city || selectedUser.provice || selectedUser.country) && (
                <div>
                  <span className="font-medium">Location:</span>
                  {" "}{selectedLocation.address},{selectedLocation.city}, {selectedLocation.province}
                </div>
              )}
              <div>
                <span className="font-medium">Role:</span> {selectedUser.roles}
              </div>
              {selectedUser.telegram && (
                <div>
                  <span className="font-medium">Telegram:</span> @{selectedUser.telegram}
                </div>
              )}

              <div className="pt-8">
                <Label htmlFor="memberType" className="mb-2 block">Select Member Type</Label>
                <Select value={memberType} onValueChange={(value: MemberType) => (setMemberType(value))}>
                  <SelectTrigger id="memberType" className="w-full">
                    <SelectValue placeholder="Select a role..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Partner">Partner</SelectItem>
                    <SelectItem value="Administrator">Administrator</SelectItem>
                    <SelectItem value="Operator">Operator</SelectItem>
                    <SelectItem value="Manager">Manager</SelectItem>
                    <SelectItem value="Owner">Owner</SelectItem>
                    <SelectItem value="Contractor">Contractor</SelectItem>
                    <SelectItem value="Technician">Technician</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          <DialogFooter className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={cancelSelection}>
              Cancel
            </Button>
            <Button
              disabled={!memberType}
              onClick={confirmSelection}
            >Confirm</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}