import { useContext, useState, useEffect } from 'react';
import { ConfigContext } from "@/context/ConfigContext";
import { postToApi } from '@/lib/api';
import { Input } from "@/components/ui/input";
import { Search, Users, Loader2 } from "lucide-react";
import { ISessionUser } from "@/types";


/**
 * Component for searching users by name, email, or phone.
 * 
 * @param {Function} onUserSelected - Function to handle user selection
 * @returns {JSX.Element} JSX Element containing the search bar and search results
 */
export function SearchUsersBar({ onUserSelected }: { onUserSelected: (user: ISessionUser) => void }): JSX.Element {
  const { user }: { user: ISessionUser } = useContext(ConfigContext);

  const [searchTerm, setSearchTerm] = useState<string>("");
  const [searchResults, setSearchResults] = useState<ISessionUser[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = async () => {
    const url: string = `/api/v1/user/search/${searchTerm}`;
    const data: any = {}
    postToApi(url, user, data,
      (response) => {
        setSearchResults(response.result);
      },
      (error) => {
        setError(error);
      }
    )
  }

  useEffect(() => {
    setLoading(true);
    const delayDebounce = setTimeout(() => {
      if (searchTerm.length < 3) {
        setSearchResults([]);
        return;
      }
      setError(null);
      fetchUsers();
      setLoading(false);
    }, 350); // debounce to avoid making too many requests
    return () => clearTimeout(delayDebounce);
  }, [searchTerm])

  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search users by name, email, or phone..."
          className="pl-9 min-h-[48px]"
        />
      </div>

      <div className="border rounded-md bg-card">
        <div className="h-[300px] overflow-y-auto bg-muted/50">
          {loading && searchTerm.length >= 3 ? (
            <div className="flex items-center justify-center h-full text-sm text-muted-foreground gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Searching...</span>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-full px-4">
              <div className="text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md p-3">
                {error}
              </div>
            </div>
          ) : searchResults.length > 0 ? (
            searchResults.map((user, index) => (
              <div
                key={user.id}
                className={`
                  p-4 cursor-pointer transition-colors hover:bg-accent hover:text-accent-foreground
                  ${index !== searchResults.length - 1 ? 'border-b border-border' : ''}
                `}
                onClick={() => onUserSelected(user)}
              >
                <div className="space-y-1">
                  <div className="font-medium text-foreground">{user.name}</div>
                  <div className="text-sm text-muted-foreground space-y-0.5">
                    <div>{user.email}</div>
                    {user.phone && <div>{user.phone}</div>}
                  </div>
                </div>
              </div>
            ))
          ) : searchTerm.length >= 3 ? (
            <div className="flex flex-col justify-center items-center h-full text-muted-foreground">
              <Users className="h-8 w-8 mb-2 opacity-50" />
              <p className="text-sm">No users found matching your search.</p>
            </div>
          ) : searchTerm.length > 0 ? (
            <div className="flex justify-center items-center h-full text-muted-foreground">
              Type at least 3 characters to search...
            </div>
          ) : (
            <div className="flex justify-center items-center h-full text-muted-foreground">
              Results will appear here...
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
