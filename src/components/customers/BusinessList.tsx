import { Card, CardContent } from "@/components/ui/card";

export function BusinessList({ businesses, onBusinessClick }) {
  return (
    <div className="space-y-4">
      {businesses && businesses.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {businesses.map((business) => (
            <Card
              key={business.id || business.name}
              className="cursor-pointer transition-all hover:shadow-md"
              onClick={() => onBusinessClick(business)}
            >
              <CardContent className="p-4">
                <h3 className="font-medium">{business.name}</h3>
                <p className="text-sm text-muted-foreground">
                  {business.city}, {business.province}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <p className="text-muted-foreground">No businesses found in this area.</p>
      )}
    </div>
  );
}