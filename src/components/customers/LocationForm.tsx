import { useState, useEffect, useRef, useContext } from "react";
import { MapPin, User, Camera, Upload, X, Loader2, Package } from "lucide-react";
import { useForm, useWatch } from "react-hook-form";
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { ConfigContext } from "@/context/ConfigContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { MultiSelect } from "@/components/ui/multi-select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useToast } from "@/components/ui/use-toast";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getFromApi, postToApi } from "@/lib/api";
import { ICityRes, IRelatedLocation, IRelatedUser, IStateRes } from "@/types";

const locationFormSchema = z.object({
  name: z.string().min(1, "Location name is required"),
  address: z.string().min(1, "Address is required"),
  city: z.string().min(1, "City is required"),
  province: z.string().min(1, "Province/State is required"),
  provinceCode: z.string().min(1, "Province/State code is required"),
  country: z.string().min(1, "Country is required"),
  postcode: z.string().optional(),
  contactName: z.string().optional(),
  phone: z.string().optional(),
  whatsapp: z.string().optional(),
  telegram: z.string().optional(),
  email: z.string().email("Invalid email format").optional().or(z.literal("")),
  website: z.string().url("Invalid website URL").optional().or(z.literal("")),
  identity: z.string().optional(),
  message: z.string().optional(),
  latlng: z.string().optional(),
  flags: z.array(z.string()).optional(),
  photo: z.any().optional(),
});

const FLAG_OPTIONS = [
  { label: "Active", value: "active" },
  { label: "Inactive", value: "inactive" },
  { label: "Maintenance", value: "maintenance" },
  { label: "Priority", value: "priority" },
  { label: "Restricted", value: "restricted" },
];

type LocationFormData = z.infer<typeof locationFormSchema>;

interface LocationFormProps {
  onSubmit: (data: LocationFormData) => void;
  onCancel: () => void;
  initialData?: Partial<IRelatedLocation>;
  mode?: 'create' | 'edit';
}

export function LocationForm({
  onSubmit,
  onCancel,
  initialData,
  mode = 'create',
}: LocationFormProps) {

  const { user }: { user: IRelatedUser } = useContext(ConfigContext)

  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [states, setStates] = useState<IStateRes[]>([]);
  const [cities, setCities] = useState<ICityRes[]>([]);
  const [loadingStates, setLoadingStates] = useState(false);
  const [loadingCities, setLoadingCities] = useState(false);
  const [geoCode, setGeoCode] = useState<string>("");
  const [selectedPhoto, setSelectedPhoto] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [selectedStateCode, setSelectedStateCode] = useState<string>("");

  const form = useForm<LocationFormData>({
    resolver: zodResolver(locationFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      address: initialData?.address || "",
      city: initialData?.city || "",
      province: initialData?.province || "",
      country: initialData?.country || "USA",
      postcode: initialData?.postcode || "",
      contactName: initialData?.contactName || "",
      phone: initialData?.phone || "",
      whatsapp: initialData?.whatsapp || "",
      telegram: initialData?.telegram || "",
      email: initialData?.email || "",
      website: initialData?.website || "",
      identity: initialData?.identity || "",
      message: initialData?.message || "",
      latlng: initialData?.latlng || "",
      flags: initialData?.flags || [],
    },
  });

  const watchedFields = useWatch({
    control: form.control,
    name: ["address", "city", "province", "country"],
  });

  useEffect(() => {
    const [address, city, province, country] = watchedFields;
    if (address && city && province && country) {
      const timer = setTimeout(() => {
        updateGeoCodeAddress();
      }, 500)
      return () => clearTimeout(timer);
    }
  }, [watchedFields]);

  useEffect(() => {
    loadStates();
    if (initialData?.photo) {
      setPhotoPreview(initialData.photo);
    }
  }, [initialData]);

  useEffect(() => {
    if (initialData?.province && states.length > 0) {
      let matchingState = states.find(state => state.state.toLowerCase() === initialData.province?.toLowerCase());
      if (matchingState) {
        setSelectedStateCode(matchingState.code);
        form.setValue("province", matchingState.state);
        form.setValue("provinceCode", matchingState.code);
        loadCities(matchingState.code);
      }
    }
  }, [initialData, states, form]);

  useEffect(() => {
    if (initialData?.city && cities.length > 0) {
      const matchingCity = cities.find(city => city.city === initialData.city);
      if (matchingCity) {
        form.setValue("city", matchingCity.city);
      }
    }
  }, [initialData, cities, form]);

  const loadStates = () => {
    if (!user) return;
    setLoadingStates(true);

    const url = `/api/v1/location/states/USA`;
    getFromApi(url, user,
      (response: { result: IStateRes[] }) => {
        if (response.result) {
          setStates(response.result);
        }
        setLoadingStates(false);
      },
      (error: any) => {
        console.error("Error loading states:", error);
        toast({
          title: "Error",
          description: "Failed to load states. Using manual input.",
          variant: "destructive",
        });
        setLoadingStates(false);
      }
    );

  };

  const loadCities = (stateCode: string) => {
    if (!user || !stateCode) return;
    setLoadingCities(true);
    setCities([]);

    const url = `/api/v1/location/cities/${stateCode}/USA`;
    getFromApi(url, user,
      (response: { result: ICityRes[] }) => {
        if (response.result) {
          const uniqueCities = Array.from(
            new Map((response.result).map(city => [city.city, city])).values()
          );
          setCities(uniqueCities);
        }
        setLoadingCities(false);
      },
      (error: any) => {
        console.error("Error loading cities:", error);
        toast({
          title: "Error",
          description: "Failed to load cities. You can enter city manually.",
          variant: "destructive",
        });
        setLoadingCities(false);
      }
    );
  };

  const handleStateChange = (stateCode: string) => {
    const matchingState = states.find(state => state.code === stateCode);
    setSelectedStateCode(stateCode);
    form.setValue("province", matchingState?.state || "");
    form.setValue("city", "");
    if (stateCode) {
      loadCities(stateCode);
    } else {
      setCities([]);
    }
  };

  const updateGeoCodeAddress = async () => {
    const address = form.getValues("address");
    const city = form.getValues("city");
    const state = form.getValues("province");
    const country = form.getValues("country");

    if (!address || !city || !state || !country) return;

    const fullAddress = `${address},${city},${state},${country}`;
    const url = `/api/v1/location/geocode/address`;
    const data = {
      address: fullAddress
    }
    console.log("url", url)
    console.log("data", data)
    postToApi(url, user, data,
      (response) => {
        console.log("geocode response:", response)
        form.setValue("latlng", response.result);
      },
      (error: any) => {
        console.error("Geocoding error:", error);
        toast({
          title: "Geocoding Error",
          description: "Geocoding service is not available. You can enter coordinates manually.",
          variant: "destructive",
        });
      }
    );
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File",
        description: "Please select an image file.",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (1MB limit)
    if (file.size > 1024 * 1024) {
      toast({
        title: "File Too Large",
        description: "Please select an image smaller than 1MB.",
        variant: "destructive",
      });
      return;
    }

    setSelectedPhoto(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPhotoPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const removePhoto = () => {
    setSelectedPhoto(null);
    setPhotoPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = (data: LocationFormData) => {
    updateGeoCodeAddress()
    const formData = {
      ...data,
      photo: selectedPhoto,
    };

    onSubmit(formData);
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <Accordion type="multiple" defaultValue={["basic", "address", "contact"]} className="w-full">

            {/* Basic Information Section */}
            <AccordionItem value="basic">
              <AccordionTrigger className="text-lg font-semibold">
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Basic Information
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Location Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter location name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="identity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Identity</FormLabel>
                        <FormControl>
                          <Input placeholder="Identity" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Message</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter location description or notes"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="flags"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Flags</FormLabel>
                      <FormControl>
                        <MultiSelect
                          options={FLAG_OPTIONS}
                          selected={field.value || []}
                          onChange={field.onChange}
                          placeholder="Select flags..."
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </AccordionContent>
            </AccordionItem>

            {/* Address & Location Section */}
            <AccordionItem value="address">
              <AccordionTrigger className="text-lg font-semibold">
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Address & Location
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-4">
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Street Address *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter street address" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="provinceCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State/Province *</FormLabel>
                        <FormControl>
                          {(
                            <Select
                              value={field.value}
                              onValueChange={(value) => {
                                field.onChange(value);
                                handleStateChange(value);
                              }}
                              disabled={loadingStates || !states.length}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder={
                                  loadingStates || !states.length ? "Loading states..." :
                                    "Select state"
                                } />
                              </SelectTrigger>
                              <SelectContent>
                                {states.map((state) => (
                                  <SelectItem key={state.code} value={state.code}>
                                    {state.state}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City *</FormLabel>
                        <FormControl>
                          {(
                            <Select
                              value={field.value}
                              onValueChange={field.onChange}
                              disabled={loadingCities || !selectedStateCode}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder={
                                  !selectedStateCode ? "Select state first" :
                                    loadingCities || !cities.length ? "Loading cities..." :
                                      "Select city"
                                } />
                              </SelectTrigger>
                              <SelectContent>
                                {cities.map((city) => (
                                  <SelectItem key={city.id} value={city.city}>
                                    {city.city}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          )}
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country *</FormLabel>
                        <FormControl>
                          <Select value={field.value} onValueChange={field.onChange}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select country" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="USA">United States</SelectItem>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="postcode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Postal Code</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter postal code" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Coordinates Section */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Coordinates</Label>
                  </div>

                  <FormField
                    control={form.control}
                    name="latlng"
                    disabled
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Latitude, Longitude</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., 40.7128,-74.0060"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                        <p className="text-xs text-muted-foreground">
                          Coordinates are automatically generated from the address.
                        </p>
                      </FormItem>
                    )}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Contact Information Section */}
            <AccordionItem value="contact">
              <AccordionTrigger className="text-lg font-semibold">
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Contact Information
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-4">
                <FormField
                  control={form.control}
                  name="contactName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter contact person name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter phone number"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="Enter email address"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="whatsapp"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>WhatsApp</FormLabel>
                        <FormControl>
                          <Input placeholder="WhatsApp number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="telegram"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Telegram</FormLabel>
                        <FormControl>
                          <Input placeholder="Telegram username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="website"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Website</FormLabel>
                        <FormControl>
                          <Input
                            type="url"
                            placeholder="https://example.com"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Photo Upload Section */}
            <AccordionItem value="photo">
              <AccordionTrigger className="text-lg font-semibold">
                <div className="flex items-center gap-2">
                  <Camera className="h-5 w-5" />
                  Photo Upload
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Location Photo</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => fileInputRef.current?.click()}
                      className="flex items-center gap-2"
                    >
                      <Upload className="h-4 w-4" />
                      Upload Photo
                    </Button>
                  </div>

                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoUpload}
                    className="hidden"
                  />

                  {photoPreview && (
                    <Card className="p-4">
                      <div className="flex items-start gap-4">
                        <div className="relative">
                          <img
                            src={photoPreview}
                            alt="Location preview"
                            className="w-32 h-32 object-cover rounded-lg border"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            onClick={removePhoto}
                            className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">
                            {selectedPhoto?.name || "Current photo"}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {selectedPhoto ? `${(selectedPhoto.size / 1024).toFixed(1)} KB` : ""}
                          </p>
                        </div>
                      </div>
                    </Card>
                  )}

                  <p className="text-xs text-muted-foreground">
                    Supported formats: JPG, PNG, GIF. Maximum size: 1MB.
                  </p>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Equipment Section (Placeholder) */}
            <AccordionItem value="equipment">
              <AccordionTrigger className="text-lg font-semibold">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Equipment Management
                  <Badge variant="outline" className="ml-2">Coming Soon</Badge>
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-4">
                <Card className="p-6">
                  <div className="text-center space-y-2">
                    <Package className="h-12 w-12 text-muted-foreground mx-auto" />
                    <h3 className="text-lg font-medium">Equipment Management</h3>
                    <p className="text-muted-foreground">
                      Equipment tracking and management features will be available in a future update.
                    </p>
                    {/* TODO: Implement equipment management functionality */}
                  </div>
                </Card>
              </AccordionContent>
            </AccordionItem>

          </Accordion>

          {/* Form Actions */}
          <div className="flex justify-end gap-4 pt-6 border-t">
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  {mode === 'edit' ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  {mode === 'edit' ? 'Update Location' : 'Create Location'}
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}