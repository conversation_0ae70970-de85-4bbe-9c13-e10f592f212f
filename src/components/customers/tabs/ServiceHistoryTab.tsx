import { useEffect } from 'react';
import { Plus, Edit, Trash2, ClipboardList } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { getFromApi } from '@/lib/api';

export function ServiceHistoryTab({ user, business, location, provider }) {
  // Sample data - replace with actual data from your business object
  const workOrders = business.workOrders || [];

  useEffect(() => {
    console.log("ServiceHistoryTab useEffect", business)

    let url = `/api/v1/provider/${business.id}/${location.id}/${provider.id}/work_order_history/90`

    if (url != '') {
      // get the equipment for this location
      getFromApi(url, user, (result) => {
        console.log("ServiceHistory results", result.result)
        setRecords(result.result)
      },
        (error) => {

        })
    }

  }, [business, location,]);

  const options = {
    dateStyle: 'short',
    timeStyle: 'short',
    hour12: true,
    day: 'numeric',
    month: 'long',
    year: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }
  let dtf = new Intl.DateTimeFormat('en-US')

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Service History</h3>
          <Button size="sm" className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Work Order
          </Button>
        </div>

        {workOrders.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Work Order ID</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Technician</TableHead>
                <TableHead>Hours</TableHead>
                <TableHead>Value</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {workOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">#{order.id}</TableCell>
                  <TableCell>{new Date(order.date).toLocaleDateString()}</TableCell>
                  <TableCell>{order.technician}</TableCell>
                  <TableCell>{order.hours}</TableCell>
                  <TableCell>${order.value.toFixed(2)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive">
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-6 border rounded-md bg-muted/10">
            <div className="flex justify-center mb-2">
              <ClipboardList className="h-10 w-10 text-muted-foreground" />
            </div>
            <p className="text-muted-foreground mb-2">No service history found for this business.</p>
            <Button size="sm" variant="outline" className="flex items-center gap-2 mx-auto">
              <Plus className="h-4 w-4" />
              Create First Work Order
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}