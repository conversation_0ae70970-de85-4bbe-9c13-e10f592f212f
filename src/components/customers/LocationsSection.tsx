import React, { useState } from "react";
import { Upload, AlertCircle } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { SearchableLocationList } from "./SearchableLocationList";
import { uploadGivenFile } from '@/lib/api';

export function LocationsSection({ user, business, provider, setSelectedLocation }) {
  const { toast } = useToast();

  const [isUploading, setIsUploading] = useState(false);
  const [isInfoDialogOpen, setIsInfoDialogOpen] = useState(false);
  const [importResultData, setImportResultData] = useState(null);
  const [format, setFormat] = useState('region');

  // Create a ref for the file input
  const fileInputRef = React.useRef(null);

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Only accept Excel files
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      toast({
        title: "Invalid File",
        description: "Please upload an Excel file (.xlsx or .xls)",
        variant: "destructive",
      });
      return;
    }

    let ff = format

    if (file.name.includes("External")) {
      ff = 'external'
    }

    setIsUploading(true);

    if (provider && user) {
      const path = `/api/v1/location/import/${provider.id}/${business.id}/${ff}`;

      uploadGivenFile("POST", path, user, {}, 'locations', file,
        () => { /* onProgress */ },
        () => { /* onLoad */ },
        (response) => {
          console.log("response:", response)
          if (response.result) {
            let addedCount = response.result.addedCount
            let errorCount = response.result.errorCount
            let locations = response.result.locations

            // remove locations already in the business.locations
            locations = locations.filter(l => !business.locations.find(x => x.id == l.id))

            business.locations = [...business.locations, ...locations];

            // If there are errors, show the error dialog
            if (errorCount > 0) {
              setImportResultData({
                addedCount: response.result.addedCount,
                errorCount: response.result.errorCount,
              });
              setIsInfoDialogOpen(true);
            }
            toast({
              title: "Upload Successful",
              description: `${addedCount} locations imported successfully`,
            });
          } else {
            throw new Error(response.message || "Upload failed");
          }
          setIsUploading(false);
          // Reset the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        },
        (error) => {
          console.error("Error uploading locations:", error);
          toast({
            title: "Upload Failed",
            description: error.message || "Could not upload locations",
            variant: "destructive",
          });
          setIsUploading(false);
          // Reset the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }

      );
    }
  };

  return (
    <div>
      <h3 className="text-lg font-medium mb-4">Locations</h3>
      <div className="flex justify-end items-right mb-2">

        <div className="flex gap-2">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileUpload}
            accept=".xlsx,.xls"
            className="hidden"
            id="excel-upload"
          />
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
          >
            <Upload className="h-4 w-4" />
            {isUploading ? "Uploading..." : "Import Locations"}
          </Button>


        </div>
      </div>

      <SearchableLocationList 
        locations={business.locations} 
        onLocationClick={(loc) => setSelectedLocation(loc)} 
        user={user}
        businessId={business.id}
      />

      {/* Result info Dialog */}
      <Dialog open={isInfoDialogOpen} onOpenChange={setIsInfoDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Batch Creation Results
            </DialogTitle>
          </DialogHeader>

          {importResultData && (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-sm font-medium text-green-800">Imported Locations</p>
                </div>

                <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-2xl font-bold text-green-700">{importResultData.addedCount}</p>
                  <p className="text-sm font-medium text-green-800">Locations Added</p>
                </div>

                <div className="bg-red-50 p-3 rounded-md border border-red-200">
                  <p className="text-sm font-medium text-red-800">Errors</p>
                  <p className="text-2xl font-bold text-red-700">{importResultData.errorCount}</p>
                </div>

              </div>

            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setIsInfoDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}