import { useEffect, useState } from "react";
import { MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";
import { SearchBar } from "@/components/shared/SearchBar";
import { FilterBar } from "@/components/shared/FilterBar";
import { LocationList } from "./LocationList";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { LocationForm } from "./LocationForm";
import { useToast } from "@/components/ui/use-toast";
import { IRelatedLocation, IRelatedUser } from "@/types";
import { postToApi } from "@/lib/api";

interface SearchableLocationListProps {
  locations: IRelatedLocation[];
  onLocationClick: (location: IRelatedLocation) => void;
  user: IRelatedUser;
  businessId?: number;
}


export function SearchableLocationList({
  locations,
  onLocationClick,
  user,
  businessId,
}: SearchableLocationListProps): JSX.Element {
  const { toast } = useToast();
  const [searchedLocations, setSearchedLocations] = useState<IRelatedLocation[]>(locations || []);
  const [filteredLocations, setFilteredLocations] = useState<IRelatedLocation[]>(locations || []);
  const [displayedLocations, setDisplayedLocations] = useState<IRelatedLocation[]>(locations || []);
  const [totalLocations, setTotalLocations] = useState<number>(locations.length || 0)
  const [creationDialogOpen, setCreationDialogOpen] = useState(false);

  useEffect(() => {
    if (locations && locations.length > 0) {
      setSearchedLocations(locations);
      setFilteredLocations(locations);
      setDisplayedLocations(locations);
    }
  }, [locations])

  useEffect(() => {
    const searchIds = new Set(searchedLocations.map(item => item.id));
    const filterIds = new Set(filteredLocations.map(item => item.id));
    const intersection = locations.filter(item =>
      searchIds.has(item.id) && filterIds.has(item.id)
    );
    setDisplayedLocations(intersection);
  }, [searchedLocations, filteredLocations, locations]);

  const updateLocationLocal = (location: IRelatedLocation) => {
    setDisplayedLocations((prevLocations) =>
      prevLocations.map((loc) => (loc.id === location.id ? location : loc))
    );
  };
  const deleteLocationLocal = (location: IRelatedLocation) => {
    setDisplayedLocations((prevLocations) =>
      prevLocations.filter((loc) => loc.id !== location.id)
    );
    setTotalLocations(prev => prev - 1)
  };
  const createLocationLocal = (location: IRelatedLocation) => {
    setDisplayedLocations((prevLocations) => [location, ...prevLocations]);
    setTotalLocations(prev => prev + 1)
  };

  const handleCreateLocation = async (formData) => {
    if (!user || !businessId) {
      toast({
        title: "Error",
        description: "User authentication and business ID are required.",
        variant: "destructive",
      });
      return;
    }
    const url = "/api/v1/location";
    const locationData = {
      name: formData.name,
      address: formData.address,
      city: formData.city,
      province: formData.province,
      country: formData.country,
      postcode: formData.postcode || "",
      contactName: formData.contactName || "",
      phone: formData.phone || "",
      whatsapp: formData.whatsapp || "",
      telegram: formData.telegram || "",
      email: formData.email || "",
      website: formData.website || "",
      identity: formData.identity || "",
      message: formData.message || "",
      latlng: formData.latlng || "",
      flags: formData.flags || [],
      businessId: businessId,
    };

    postToApi(url, user, locationData,
      (response) => {
        console.log("Location created successfully:", response);
        toast({
          title: "Success",
          description: "Location created successfully.",
        });
        setCreationDialogOpen(false);
        createLocationLocal(response.result);
      },
      (error) => {
        console.error("Error creating location:", error);
        toast({
          title: "Error",
          description: `Failed to create location: ${error}`,
          variant: "destructive",
        });
      }
    );
  };
  return (
    <>
      <div className="space-y-4">
        {/* Search Bar */}
        <SearchBar<IRelatedLocation>
          config={{
            searchFields: ['name', 'address', 'city', 'contactName', 'identity', 'phone', 'flags'],
            placeholder: "Search locations...",
            minSearchLength: 2,
            debounceDelay: 100
          }}
          items={locations}
          onResults={(results) => setSearchedLocations(results)}
        />

        {/* State Filter Buttons */}
        <FilterBar<IRelatedLocation>
          items={locations}
          config={{ filterProperty: 'province' }}
          onResults={(results) => setFilteredLocations(results)}
        />

        {/* Results Summary */}
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <span>
            Showing {displayedLocations.length} of {totalLocations} locations
          </span>
          <Button
            variant="outline"
            size="lg"
            onClick={() => setCreationDialogOpen(true)}
          >
            Add new location
          </Button>
        </div>

        {/* Location List */}
        {displayedLocations.length > 0 ? (
          <LocationList
            locations={displayedLocations}
            onLocationClick={onLocationClick}
            onEdit={updateLocationLocal}
            onDelete={deleteLocationLocal}
            user={user}
          />
        ) : locations && locations.length === 0 ? (
          <p className="text-muted-foreground">No Locations for this business.</p>
        ) : (
          <div className="text-center py-8">
            <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              No locations found matching your search or filters.
            </p>
            <p className="text-sm text-muted-foreground mt-2">
              Try adjusting your filters or search terms
            </p>
          </div>
        )}
      </div>
      {/* Location Creation Dialog */}
      <Dialog open={creationDialogOpen} onOpenChange={setCreationDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-primary" />
              Create New Location
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <LocationForm
              onSubmit={handleCreateLocation}
              onCancel={() => setCreationDialogOpen(false)}
              mode="create"
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}