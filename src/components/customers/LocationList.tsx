import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
  DialogDescription
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { LocationForm } from "./LocationForm";
import { Edit, Trash2, MapPin, Phone, Mail, AlertTriangle } from "lucide-react";
import { IRelatedLocation, IRelatedUser } from "@/types";
import { putToApi, deleteToApi } from "@/lib/api";

interface LocationListProps {
  locations: IRelatedLocation[];
  onLocationClick: (location: IRelatedLocation) => void;
  onEdit: (location: IRelatedLocation) => void;
  onDelete: (location: IRelatedLocation) => void;
  user: IRelatedUser;
}

export function LocationList({
  locations,
  onLocationClick,
  onEdit,
  onDelete,
  user,
}: LocationListProps) {
  const { toast } = useToast();
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<IRelatedLocation | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleEditClick = (e: React.MouseEvent, location: IRelatedLocation) => {
    e.stopPropagation();
    setSelectedLocation(location);
    setEditDialogOpen(true);
  };

  const handleDeleteClick = (e: React.MouseEvent, location: IRelatedLocation) => {
    e.stopPropagation();
    setSelectedLocation(location);
    setDeleteDialogOpen(true);
  };

  const handleEditSubmit = (formData) => {
    if (!user || !selectedLocation) {
      toast({
        title: "Error",
        description: "User authentication and location selection are required.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    const locationData = {
      name: formData.name,
      address: formData.address,
      city: formData.city,
      province: formData.province,
      country: formData.country,
      postcode: formData.postcode || "",
      contactName: formData.contactName || "",
      phone: formData.phone || "",
      whatsapp: formData.whatsapp || "",
      telegram: formData.telegram || "",
      email: formData.email || "",
      website: formData.website || "",
      identity: formData.identity || "",
      message: formData.message || "",
      latlng: formData.latlng || "",
      flags: formData.flags || [],
    };

    const url = `/api/v1/location/${selectedLocation.id}`;
    putToApi(url, user, locationData,
      (response) => {
        console.log("Location updated successfully:", response);
        toast({
          title: "Success",
          description: "Location updated successfully.",
        });
        setEditDialogOpen(false);
        setSelectedLocation(null);
        setIsSubmitting(false);
        onEdit(response.result);
      },
      (error) => {
        console.error("Error updating location:", error);
        toast({
          title: "Error",
          description: `Failed to update location: ${error}`,
          variant: "destructive",
        });
        setIsSubmitting(false);
      }
    );
  };

  const handleDeleteConfirm = () => {
    if (!user || !selectedLocation) {
      toast({
        title: "Error",
        description: "User authentication and location selection are required.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    const url = `/api/v1/location/${selectedLocation.id}`;
    const data = {}
    deleteToApi(url, user, data,
      (response) => {
        console.log("Location deleted successfully:", response);
        toast({
          title: "Success",
          description: "Location deleted successfully.",
        });
        setDeleteDialogOpen(false);
        setSelectedLocation(null);
        setIsSubmitting(false);
        onDelete(selectedLocation);
      },
      (error) => {
        console.error("Error deleting location:", error);
        toast({
          title: "Error",
          description: `Failed to delete location: ${error}`,
          variant: "destructive",
        });
        setIsSubmitting(false);
      }
    );
  };

  return (
    <>
      <div className="space-y-4">
        {locations && locations.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {locations.map((loc) => (
              <Card
                key={loc.id || loc.name}
                className="cursor-pointer transition-all hover:shadow-md group"
                onClick={() => onLocationClick(loc)}
              >
                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium text-lg">{loc.name}</h3>
                    <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => handleEditClick(e, loc)}
                        className="h-8 w-8 p-0"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => handleDeleteClick(e, loc)}
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {loc.address}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      {loc.city}, {loc.province}
                    </p>

                    {loc.contactName && (
                      <p className="text-sm text-muted-foreground">
                        Contact: {loc.contactName}
                      </p>
                    )}

                    {loc.phone && (
                      <p className="text-sm text-muted-foreground flex items-center gap-1">
                        <Phone className="h-3 w-3" />
                        {loc.phone}
                      </p>
                    )}

                    {loc.email && (
                      <p className="text-sm text-muted-foreground flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {loc.email}
                      </p>
                    )}

                    {loc.flags && loc.flags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {loc.flags.map((flag, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {flag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <p className="text-muted-foreground">No Locations for this business.</p>
        )}
      </div>

      {/* Edit Location Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5 text-primary" />
              Edit Location
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedLocation && (
              <LocationForm
                onSubmit={handleEditSubmit}
                onCancel={() => {
                  setEditDialogOpen(false);
                  setSelectedLocation(null);
                }}
                initialData={selectedLocation}
                mode="edit"
              />
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-destructive" />
              Delete Location
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedLocation?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setDeleteDialogOpen(false);
                setSelectedLocation(null);
              }}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Deleting..." : "Delete"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}