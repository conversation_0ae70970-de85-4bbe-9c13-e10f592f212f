import React, { useState, useEffect } from "react";

import { putTo<PERSON>pi, postTo<PERSON>pi, putWithImage,  } from "../lib/api";
import { latLngFromString, round } from "../lib/utils";

export default function MapSelector(props) {

  const { title, user, position, location } = props

  const [map, setMap] = useState(null)
  const [zoom, setZoom] = useState(16); // initial zoom
  const [center, setCenter] = useState( null);
  const [marker, setMarker] = useState(null)

  useEffect(() => {
    if (position || location.latlng) {

      //console.log("useEffect position", position)

      if (!center) {

        if (position) {
          let center = {
            lat: position[0],
            lng: position[1],
          }
          setCenter(center)
        } else {
          let latlon = latLngFromString(location.latlng)
          //let here = LatLon(latlon[0], latlon[1]);
          let center = {
            lat: latlon[0],
            lng: latlon[1],
          }
          setCenter(
            center
          )
        }
      }

      if (!map && center) {
        //console.log("create map")
        let map = L.map('map').setView([center.lat, center.lng], zoom);

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          maxZoom: 19,
          attribution: '© OpenStreetMap'
        }).addTo(map);


      // add the map marker
      let marker = L.marker([center.lat, center.lng]).addTo(map);

        var popup = L.popup();

        function onMapClick(e) {
          popup
            .setLatLng(e.latlng)
            .setContent("Click @ " + round(e.latlng.lat, 6) + ", " + round(e.latlng.lng, 6))
            .openOn(map);

          setCenter({
            lat: round(e.latlng.lat, 6),
            lng: round(e.latlng.lng, 6)
          })

          props.onMapClick({
            lat: round(e.latlng.lat, 6),
            lng: round(e.latlng.lng, 6)
          })

          // remove the marker from the map
          map.removeLayer(marker)
          marker = L.marker([e.latlng.lat, e.latlng.lng]).addTo(map);

        }

        map.on('click', onMapClick);

        setMap(map)
      }

      if (map && position) {
        //console.log("setView", position)
        map.setView(position, zoom)
      }


    }
  }, [position, location, center]);


  //console.log('map @', center)
  return (
    <div className="card pt-3 pt-xl-4">
     {title && <h3 className="px-2 px-sm-2 px-lg-3 mx-xl-4 pb-4">{title}</h3>} 
     
      {center && <p className="text-sm font-medium text-muted-foreground my-2">Latitude: {center.lat} , Longitude: {center.lng}</p>}

      <div id="map" style={{ display: "flex", height: 400, marginTop: 20 }}>

        {/* Basic form for controlling center and zoom of map. */}

      </div>

    </div>
  )
}