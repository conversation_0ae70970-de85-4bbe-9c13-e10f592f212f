
import { NavLink, useLocation } from 'react-router-dom';
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Users, FileText, Home, Receipt, User, Package, Box, Truck } from "lucide-react";

interface SidebarProps {
  className?: string;
  isOpen: boolean;
}

interface SidebarItemProps {
  icon: React.ElementType;
  label: string;
  path: string;
}

function SidebarItem({ icon: Icon, label, path }: SidebarItemProps) {
  return (
    <NavLink
      to={path}
      className={({ isActive }) => cn(
        isActive ? "text-primary" : "text-sidebar-foreground"
      )}
    >
      {({ isActive }) => (
        <Button
          variant={isActive ? "secondary" : "ghost"}
          className={cn(
            "w-full justify-start gap-2",
            isActive && "bg-primary/10 dark:bg-primary/30 font-medium"
          )}
        >
          <Icon className="h-5 w-5" />
          <span>{label}</span>
        </Button>
      )}
    </NavLink>
  );
}

export function Sidebar({ className, isOpen }: SidebarProps) {
  const location = useLocation();
  
  const analyticsItems =  () => {
    return (
     
      <div className="space-y-2">
      <h3 className="text-xs font-medium text-sidebar-foreground/70 px-2">
        ANALYTICS
      </h3>
      <div className="space-y-1">
        <Button variant="ghost" className="w-full justify-start gap-2">
          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="3" y="8" width="4" height="10" rx="1" />
            <rect x="10" y="4" width="4" height="14" rx="1" />  
            <rect x="17" y="12" width="4" height="6" rx="1" />
          </svg>
          <span>Reports</span>
        </Button>
        <Button variant="ghost" className="w-full justify-start gap-2">
          <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" />
            <path d="M2 12h20" />
          </svg>
          <span>Performance</span>
        </Button>
      </div>
    </div>
    );
  };

  return (
    <aside
      className={cn(
        "fixed inset-y-0 left-0 z-30 flex w-60 flex-col border-r bg-sidebar transition-transform duration-300 ease-in-out",
        isOpen ? "translate-x-0" : "-translate-x-full",
        className
      )}
    >
      <div className="flex h-16 items-center border-b px-6">
        <div className="flex items-center gap-2">
        <img src="/logo.png" alt="OpenZcan" className="h-8 w-8" />
          <span className="font-bold text-xl">OZ</span>
        </div>
      </div>
      <ScrollArea className="flex-1 px-4 py-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-xs font-medium text-sidebar-foreground/70 px-2">
              MAIN MENU
            </h3>
            <div className="space-y-1">
              <SidebarItem icon={Home} label="Dashboard" path="/"/>
              <SidebarItem icon={Users} label="Customers" path="/customers"/>
              <SidebarItem icon={FileText} label="Work Orders" path="/work-orders"/>
              <SidebarItem icon={Receipt} label="Invoices" path="/invoices"/>
              <SidebarItem icon={User} label="Team" path="/team"/>
              <SidebarItem icon={Package} label="Inventory" path="/inventory"/>
              <SidebarItem icon={Box} label="Bins" path="/bins"/>
              <SidebarItem icon={Truck} label="Vehicles" path="/vehicles"/>
              <SidebarItem icon={Users} label="Sub Contractors" path="/contractors"/>
            </div>
          </div>

        </div>
      </ScrollArea>
     {/*  <div className="border-t p-4">
        <Button variant="outline" className="w-full">
          <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="8" x2="12" y2="16" />
            <line x1="8" y1="12" x2="16" y2="12" />
          </svg>
          Add New Service
        </Button>
      </div> */}
    </aside>
  );
}
