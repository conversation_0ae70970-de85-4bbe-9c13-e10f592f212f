import React, { useEffect, useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Plus } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { INewBatch } from "./BatchManagementView";
import { IInvoiceRes } from '@/types/invoices';
import { CustomerSelector } from './CustomerSelector';
import { ICustomerRes } from '@/types';

interface CreateBatchTabProps {
  invoices: IInvoiceRes[];
  customers: ICustomerRes[];
  selectedCustomer: ICustomerRes | null;
  onCustomerChange: (customer: any) => void;
  onBatchCreate: (batch: INewBatch, selectedInvoices: IInvoiceRes[]) => void;
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
  className?: string;
}

export function CreateBatchTab({
  invoices,
  customers,
  selectedCustomer,
  onCustomerChange,
  onBatchCreate,
  onSuccess,
  onError,
  className = ""
}: CreateBatchTabProps) {
  const [selectedInvoices, setSelectedInvoices] = useState<IInvoiceRes[]>([]);
  const [batchName, setBatchName] = useState<string>('');
  const [batchNameError, setBatchNameError] = useState<string>('');

  // Generate default batch name with timestamp
  const generateDefaultBatchName = (customerName: string) => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    return `${customerName} Batch ${year}-${month}-${day}_${hours}-${minutes}`;
  };

  const validateBatchName = (name: string): boolean => {
    const trimmedName = name.trim();
    if (!trimmedName) {
      setBatchNameError('Batch name cannot be empty');
      return false;
    }
    if (trimmedName.length < 3) {
      setBatchNameError('Batch name must be at least 3 characters long');
      return false;
    }
    if (trimmedName.length > 50) {
      setBatchNameError('Batch name cannot exceed 50 characters');
      return false;
    }
    setBatchNameError('');
    return true;
  };

  const handleBatchNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setBatchName(newName);
    if (batchNameError) {
      validateBatchName(newName);
    }
  };

  const filteredInvoices = useMemo(() => {
    if (!selectedCustomer) return [];
    return invoices.filter(invoice =>
      invoice.businessId.toString() === selectedCustomer.id.toString()
    );
  }, [invoices, selectedCustomer]);

  useEffect(() => {
    setSelectedInvoices([]);
    // Generate new batch name when customer changes
    if (selectedCustomer) {
      setBatchName(generateDefaultBatchName(selectedCustomer.name || 'Customer'));
      setBatchNameError('');
    } else {
      setBatchName('');
      setBatchNameError('');
    }
  }, [selectedCustomer]);

  const handleInvoiceSelection = (invoice: any, checked: boolean) => {
    setSelectedInvoices(prev =>
      checked
        ? [...prev, invoice]
        : prev.filter(inv => inv.id !== invoice.id)
    );
  };

  const createBatchFromSelectedInvoices = () => {
    if (selectedInvoices.length === 0) {
      const errorMsg = "Please select at least one invoice to create a batch";
      onError?.(errorMsg);
      return;
    }

    if (!selectedCustomer) {
      const errorMsg = "Please select a customer first";
      onError?.(errorMsg);
      return;
    }

    if (!validateBatchName(batchName)) {
      onError?.("Please provide a valid batch name");
      return;
    }

    const totalAmount = selectedInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0);

    const newBatch: INewBatch = {
      name: batchName.trim(),
      status: "draft",
      invoiceCount: selectedInvoices.length,
      totalAmount,
      createdAt: new Date().toISOString().split('T')[0],
      customFields: []
    };

    onBatchCreate(newBatch, selectedInvoices);

    setSelectedInvoices([]);

    const successMsg = `Created batch "${batchName.trim()}" with ${selectedInvoices.length} invoices for ${selectedCustomer?.name}`;
    onSuccess?.(successMsg);
  };

  const selectAllInvoices = () => {
    setSelectedInvoices([...filteredInvoices]);
  };

  const clearSelection = () => {
    setSelectedInvoices([]);
  };

  return (
    <div className={className}>
      <div className="space-y-4">
        {/* Customer Selection */}
        <CustomerSelector
          customers={customers}
          selectedCustomer={selectedCustomer}
          onCustomerChange={onCustomerChange}
          label="Select Customer for Batch"
          placeholder="Choose a customer to create batch for..."
          required={true}
          showDetails={true}
        />

        {/* Batch Name Input */}
        {selectedCustomer && (
          <Card>
            <CardHeader>
              <CardTitle>Batch Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="batch-name">Batch Name</Label>
                <Input
                  id="batch-name"
                  type="text"
                  placeholder="Enter batch name..."
                  value={batchName}
                  onChange={handleBatchNameChange}
                  className={`${batchNameError ? 'border-destructive' : ''}`}
                  onBlur={() => validateBatchName(batchName)}
                />
                {batchNameError && (
                  <span className="text-xs text-destructive">{batchNameError}</span>
                )}
                <p className="text-xs text-muted-foreground">
                  This name will be used to identify the batch in the system.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Invoice Selection Card */}
        {selectedCustomer && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Create Batch from Existing Invoices
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    Select invoices to add to a new batch ({filteredInvoices.length} available)
                  </p>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={selectAllInvoices}
                      disabled={filteredInvoices.length === 0}
                    >
                      Select All
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearSelection}
                    >
                      Clear Selection
                    </Button>
                    <Button
                      onClick={createBatchFromSelectedInvoices}
                      disabled={selectedInvoices.length === 0 || !!batchNameError || !batchName.trim()}
                      className="flex items-center gap-2"
                    >
                      <Plus className="h-4 w-4" />
                      Create Batch ({selectedInvoices.length})
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <div className="flex items-center justify-center h-full min-h-[3rem]">
                            <Checkbox
                              checked={selectedInvoices.length === filteredInvoices.length && filteredInvoices.length > 0}
                              onCheckedChange={(checked) => {
                                setSelectedInvoices(checked ? [...filteredInvoices] : []);
                              }}
                              className="relative"
                            />
                          </div>
                        </TableHead>
                        <TableHead>Invoice #</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredInvoices.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center text-muted-foreground py-8">
                            No invoices found for the selected customer
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredInvoices.map((invoice) => (
                          <TableRow key={invoice.id}>
                            <TableCell className="align-middle">
                              <div className="flex items-center justify-center h-full min-h-[2.5rem]">
                                <Checkbox
                                  checked={selectedInvoices.some(inv => inv.id === invoice.id)}
                                  onCheckedChange={(checked) => {
                                    handleInvoiceSelection(invoice, checked as boolean);
                                  }}
                                  className="relative"
                                />
                              </div>
                            </TableCell>
                            <TableCell className="font-medium">
                              #{invoice.number || invoice.id}
                            </TableCell>
                            <TableCell>
                              {new Date(invoice.CreatedAt).toLocaleDateString()}
                            </TableCell>
                            <TableCell>${(invoice.total || 0).toFixed(2)}</TableCell>
                            <TableCell>
                              <Badge variant={
                                invoice.status === 'paid' ? 'default' :
                                  invoice.status === 'sent' ? 'secondary' :
                                    invoice.status === 'overdue' ? 'destructive' : 'outline'
                              }>
                                {invoice.status}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
