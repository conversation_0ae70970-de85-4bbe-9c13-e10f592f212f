import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { FileText, Check } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { postToApi, patchToApi } from '@/lib/api';


export function WorkOrderInvoiceDialog({ isOpen, setIsOpen, user, provider, workOrders, setWorkOrders, setInvoices }) {

    const { toast } = useToast();

    const createInvoice = (workOrderId) => {
        if (provider && user) {
            const url = `/api/v1/invoice/${provider.id}/work_order/${workOrderId}/invoice`;
            const data = {}
            console.log("url:", url)
            console.log("data:", data)
            postToApi(url, user, data,
                (response) => {
                    // Update work order status to invoiced
                    const statusUrl = `/api/v1/business/${provider.id}/work_order/${workOrderId}`;

                    patchToApi(statusUrl, user, { status: 'invoiced' },
                        (response) => {
                            console.log("Work order status updated:", response);
                        },
                        (error) => {
                            console.error("Error updating work order status:", error);
                        }
                    );

                    // Add the new invoice to the list
                    setInvoices(prev => [...prev, response.result].sort((a, b) => a.id - b.id));

                    // Remove the work order from the completed list
                    setWorkOrders(prev => prev.filter(wo => wo.id !== workOrderId));

                    toast({
                        title: "Invoice Created",
                        description: `Invoice #${response.result.number} created successfully`,
                    });
                },
                (error) => {
                    console.error("Error creating invoice:", error);
                    toast({
                        title: "Invoice Creation Failed",
                        description: "Could not create invoice from work order",
                        variant: "destructive",
                    });
                }
            );
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                    <DialogTitle>Create Invoice from Work Order</DialogTitle>
                </DialogHeader>

                {workOrders.length > 0 ? (
                    <div className="py-4">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Work Order #</TableHead>
                                    <TableHead>Customer</TableHead>
                                    <TableHead>Description</TableHead>
                                    <TableHead>Date</TableHead>
                                    <TableHead>Action</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {workOrders.map((workOrder) => (
                                    <TableRow key={workOrder.id}>
                                        <TableCell className="font-medium">#{workOrder.num}</TableCell>
                                        <TableCell>{workOrder.business?.name || 'N/A'}</TableCell>
                                        <TableCell className="max-w-[200px] truncate" title={workOrder.description}>
                                            {workOrder.description || 'No description'}
                                        </TableCell>
                                        <TableCell>{new Date(workOrder.started).toLocaleDateString()}</TableCell>
                                        <TableCell>
                                            <Button
                                                size="sm"
                                                className="flex items-center gap-1"
                                                onClick={() => {
                                                    createInvoice(workOrder.id);
                                                    setIsOpen(false);
                                                }}
                                            >
                                                <Check className="h-4 w-4" />
                                                Create
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                ) : (
                    <div className="text-center py-8">
                        <div className="flex justify-center mb-4">
                            <FileText className="h-12 w-12 text-muted-foreground" />
                        </div>
                        <h3 className="text-lg font-medium mb-2">No Completed Work Orders</h3>
                        <p className="text-muted-foreground">
                            There are no completed work orders available to invoice.
                        </p>
                    </div>
                )}
            </DialogContent>
        </Dialog>
    )
}