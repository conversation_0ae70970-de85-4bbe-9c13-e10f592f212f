import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Download } from "lucide-react";
import { Checkbox } from '@radix-ui/react-checkbox';
import { IBatchResponse } from "@/types/invoices";
import { getFromApi } from '@/lib/api';
import { useToast } from "@/components/ui/use-toast";

export interface ExportOptions {
  includeCustomFields: boolean;
  includeCustomerDetails: boolean;
  includeTotals: boolean;
}

export interface ExportConfig {
  batchId: string;
  format: 'csv' | 'xlsx' | 'pdf';
  options: ExportOptions;
  batchNumber: string;
}

interface ExportBatchTabProps {
  user: any;
  batches: IBatchResponse[];
  className?: string;
}

export function ExportBatchTab({
  user,
  batches,
  className = ""
}: ExportBatchTabProps) {
  const [selectedBatchId, setSelectedBatchId] = useState<string>("");
  const [exportFormat, setExportFormat] = useState<'csv' | 'xlsx' | 'pdf'>('csv');
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    includeCustomFields: true,
    includeCustomerDetails: true,
    includeTotals: true
  });
  const { toast } = useToast();

  const handleExport = () => {
    if (!selectedBatchId) {
      toast({
        title: "Export Failed",
        description: "Please select a batch to export",
        variant: "destructive"
      });
      return;
    }

    // find the batch to get the batch num
    const batch = batches.find(b => b.id === Number(selectedBatchId));

    const url = `/api/v1/app/invoice/batch/${selectedBatchId}/${exportFormat}`;
    console.log("url:", url)
    getFromApi(url, user, 
    (response)=>{
      toast({
        title: "Export Started",
        description: "Your batch export is being prepared for download",
      });
      console.log("Export response: ", response);
    }, 
    (response)=>{
      console.log("Error response: ", response);
    })
  };

  const updateExportOption = (option: keyof ExportOptions, value: boolean) => {
    setExportOptions(prev => ({
      ...prev,
      [option]: value
    }));
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Batches
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div>
                <Label htmlFor="export-batch">Select Batch</Label>
                <Select value={selectedBatchId} onValueChange={setSelectedBatchId}>
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder="Choose a batch to export" />
                  </SelectTrigger>
                  <SelectContent>
                    {batches.map((batch) => (
                      <SelectItem key={batch.id} value={batch.id.toString()}>
                        {batch.num} ({batch.Invoices.length} invoices)
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="export-format">Export Format</Label>
                <Select 
                  value={exportFormat} 
                  onValueChange={(value: 'csv' | 'xlsx' | 'pdf') => setExportFormat(value)}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="csv">CSV</SelectItem>
                    <SelectItem value="xlsx">Excel (XLSX)</SelectItem>
                    <SelectItem value="pdf">PDF Report</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <div className="border rounded-md p-4 bg-muted/10">
                <h4 className="font-medium mb-2">Export Options</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="include-custom-fields" 
                      checked={exportOptions.includeCustomFields}
                      onCheckedChange={(checked) => 
                        updateExportOption('includeCustomFields', checked as boolean)
                      }
                    />
                    <Label htmlFor="include-custom-fields" className="text-sm">
                      Include custom fields
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="include-customer-details" 
                      checked={exportOptions.includeCustomerDetails}
                      onCheckedChange={(checked) => 
                        updateExportOption('includeCustomerDetails', checked as boolean)
                      }
                    />
                    <Label htmlFor="include-customer-details" className="text-sm">
                      Include customer details
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="include-totals" 
                      checked={exportOptions.includeTotals}
                      onCheckedChange={(checked) => 
                        updateExportOption('includeTotals', checked as boolean)
                      }
                    />
                    <Label htmlFor="include-totals" className="text-sm">
                      Include batch totals
                    </Label>
                  </div>
                </div>
              </div>

              <Button
                className="w-full flex items-center gap-2"
                onClick={handleExport}
                disabled={!selectedBatchId}
              >
                <Download className="h-4 w-4" />
                Export Batch
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
