import React, { useState, useRef } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/components/ui/use-toast";
import { InvoicePreviewCard } from "./CSVInvoicesPreviewCard";
import { CustomerSelector } from './CustomerSelector';
import {
  Upload,
  Download,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { INewInvoice } from "./BatchManagementView";
import { ICustomerRes } from '@/types';
import * as XLSX from 'xlsx';

type CsvRow = Record<string, any>;

const DEFAULT_REQUIRED_COLUMNS = [
  // 'notes',
  // 'tax',
  // 'subTotal',
  // 'total',
];

interface FileImportTabProps {
  customers: ICustomerRes[];
  selectedCustomer: ICustomerRes | null;
  onCustomerChange: (customer: ICustomerRes) => void;
  onBatchCreate: (file: File, invoices: INewInvoice[], fileExtension?: string, batchName?: string, newBatch?: any) => void;
  className?: string;
}

export function CSVImportTab({
  customers,
  selectedCustomer,
  onCustomerChange,
  onBatchCreate,
  className = ""
}: FileImportTabProps) {
  const [csvData, setCsvData] = useState<any[]>([]);
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [validInvoices, setValidInvoices] = useState<INewInvoice[]>([]);
  const [invalidInvoices, setInvalidInvoices] = useState<INewInvoice[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [invoiceValidationMap, setInvoiceValidationMap] = useState<Record<number, string[]>>({});
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const downloadSampleCSV = () => {
    const headers = [...DEFAULT_REQUIRED_COLUMNS, 'customField1'];
    const sampleData = [
      'Some notes,150.00,1500.00,1650.00,Sales'
    ];

    const csvContent = [headers.join(','), ...sampleData].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'invoice_batch_template.csv';
    link.click();
    window.URL.revokeObjectURL(url);

    toast({
      title: "Template Downloaded",
      description: "Sample CSV template has been downloaded",
    });
  };

  const isExcelFile = (fileName: string): boolean => {
    const extension = fileName.toLowerCase().split('.').pop();
    return extension === 'xlsx' || extension === 'xls';
  }

  const normalizeExcelValue = (value: any): string => {
    if (value === null || value === undefined) return '';

    // Handle Excel date values (they come as numbers)
    if (typeof value === 'number' && value > 25000 && value < 50000) {
      try {
        const date = XLSX.SSF.parse_date_code(value);
        if (date) {
          return `${date.y}-${String(date.m).padStart(2, '0')}-${String(date.d).padStart(2, '0')}`;
        }
      } catch (e) {
        // If date parsing fails, treat as regular number
      }
    }

    return String(value).trim();
  }

  const parseExcelFile = (file: File): Promise<{ headers: string[], rowsData: any[] }> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = e.target?.result;
          if (!data) {
            reject(new Error("Failed to read Excel file"));
            return;
          }

          const workbook = XLSX.read(data, { type: 'array' });

          if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
            reject(new Error("Excel file contains no worksheets"));
            return;
          }

          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];

          if (!worksheet || Object.keys(worksheet).length === 0) {
            reject(new Error("Excel worksheet is empty"));
            return;
          }

          // Convert worksheet to JSON array with headers in first row
          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            header: 1, // Use first row as headers
            defval: '', // Default value for empty cells
            raw: true // Keep raw values to handle dates and numbers properly
          }) as any[][];

          if (jsonData.length < 2) {
            reject(new Error("Excel file must contain headers and at least one data row"));
            return;
          }

          const headers = jsonData[0]
            .map((h: any) => normalizeExcelValue(h))
            .filter(h => h !== '');

          if (headers.length === 0) {
            reject(new Error("Excel file must contain valid column headers"));
            return;
          }

          const rowsData = jsonData.slice(1)
            .filter(row => row.some(cell => cell !== '' && cell !== null && cell !== undefined)) // Filter out completely empty rows
            .map(row => {
              const rowObj: any = {};
              headers.forEach((header: string, index: number) => {
                const cellValue = row[index];
                rowObj[header] = normalizeExcelValue(cellValue);
              });
              return rowObj;
            });

          if (rowsData.length === 0) {
            reject(new Error("Excel file contains no data rows"));
            return;
          }

          resolve({ headers, rowsData });
        } catch (error) {
          console.error('Excel parsing error:', error);
          reject(new Error(`Failed to parse Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
      };

      reader.onerror = () => reject(new Error("Failed to read Excel file"));
      reader.readAsArrayBuffer(file);
    });
  }

  const validateInvoice = (invoice: CsvRow): string[] => {
    const errors: string[] = [];

    //TODO: Check mandatory fields
    //if (isNaN(invoice.subTotal) || invoice.subTotal < 0) errors.push("Subtotal must be a positive number");
    //if (isNaN(invoice.tax) || invoice.tax < 0) errors.push("Tax must be a positive number");
    //if (isNaN(invoice.total) || invoice.total < 0) errors.push("Total must be a positive number");

    //TODO: check is this is necessary
    if (selectedCustomer) {
      if (invoice.customerName &&
          invoice.customerName.toLowerCase() !== selectedCustomer.name.toLowerCase()) {
        errors.push(`Customer name "${invoice.customerName}" does not match selected customer "${selectedCustomer.name}"`);
      }

      const customerEmail = selectedCustomer.User?.email || selectedCustomer.email;
      if (invoice.email && customerEmail &&
          invoice.email.toLowerCase() !== customerEmail.toLowerCase()) {
        errors.push(`Customer email "${invoice.email}" does not match selected customer email "${customerEmail}"`);
      }
    }

    return errors;
  };

  const extractCustomFields = (row: any, headers: string[]) => {
    const customFields: any = {};
    headers.forEach(header => {
      if (!DEFAULT_REQUIRED_COLUMNS.includes(header)) {
        customFields[header] = row[header];
      }
    });
    return customFields;
  };

  const mapRowToInvoice = (row: CsvRow, headers: string[]): INewInvoice => {
    return {
      customerName: row.customerName || '',
      email: row.email || '',
      phone: row.phone || '',
      country: row.country || '',
      province: row.province || '',
      city: row.city || '',
      address: row.address || '',
      notes: row.notes || '',
      tax: parseFloat(row.tax) || 0,
      subTotal: parseFloat(row.subTotal) || 0,
      total: parseFloat(row.total) || 0,
      dueDate: row.dueDate || '',
      status: "draft",
      customFields: extractCustomFields(row, headers)
    };
  };

  const splitValidAndInvalidRows = (rows: CsvRow[]) => {
    const validRows: CsvRow[] = [];
    const invalidRows: CsvRow[] = [];
    const newValidationMap: Record<number, string[]> = {};

    rows.forEach((row, index) => {
      const errors = validateInvoice(row);
      if (errors.length > 0) {
        invalidRows.push(row);
        newValidationMap[index] = errors;
      } else {
        validRows.push(row);
      }
    });

    setInvoiceValidationMap(newValidationMap);
    return { validRows, invalidRows };
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploadedFile(file);

    if (!selectedCustomer) {
      const errorMsg = "Please select a customer before uploading file";
      toast({
        title: "Customer Required",
        description: errorMsg,
        variant: "destructive"
      });
      // Clear the file input
      if (event.target) {
        event.target.value = '';
      }
      return;
    }

    try {
      let headers: string[];
      let rowsData: any[];

      if (isExcelFile(file.name)) {
        try {
          const excelData = await parseExcelFile(file);
          headers = excelData.headers;
          rowsData = excelData.rowsData;
          //console.log(`Successfully parsed Excel file with ${headers.length} columns and ${rowsData.length} rows`);
        } catch (excelError) {
          const errorMsg = `Excel parsing failed: ${excelError instanceof Error ? excelError.message : 'Unknown error'}`;
          toast({
            title: "Excel Parse Error",
            description: errorMsg,
            variant: "destructive"
          });
          return;
        }
      } else {
        // Parse CSV file
        const text = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = (e) => resolve(e.target?.result as string);
          reader.onerror = () => reject(new Error("Failed to read file"));
          reader.readAsText(file);
        });

        const lines = text.split('\n').filter(line => line.trim());
        if (lines.length < 2) {
          const errorMsg = "File must contain headers and at least one data row";
          toast({
            title: "Invalid File",
            description: errorMsg,
            variant: "destructive"
          });
          return;
        }

        headers = lines[0].split(',').map((h: string) => h.trim().replace(/"/g, ''));
        rowsData = lines.slice(1).map(line => {
          const values = line.split(',').map(v => v.trim().replace(/"/g, ''));
          const row: any = {};
          headers.forEach((header: string, index: number) => {
            row[header] = values[index] || '';
          });
          return row;
        });
      }

      // Check for required columns
      const missingColumns = DEFAULT_REQUIRED_COLUMNS.filter(required => !headers.includes(required));
      if (missingColumns.length > 0) {
        const errorMsg = `Missing required columns: ${missingColumns.join(', ')}`;
        toast({
          title: "Invalid File Format",
          description: errorMsg,
          variant: "destructive"
        });
        return;
      }

      setCsvHeaders(headers);
      setCsvData(rowsData);

      const { validRows, invalidRows } = splitValidAndInvalidRows(rowsData);
      setValidInvoices(validRows.map(row => mapRowToInvoice(row, headers)));
      setInvalidInvoices(invalidRows.map(row => mapRowToInvoice(row, headers)));

      setShowPreview(true);

    } catch (error) {
      const errorMsg = `Failed to parse file: ${error instanceof Error ? error.message : 'Unknown error'}`;
      toast({
        title: "File Parse Error",
        description: errorMsg,
        variant: "destructive"
      });
    }
  };

  const processCsvImport = async (batchName: string) => {
    if (validInvoices.length === 0) {
      const errorMsg = "No valid invoices found to create a batch";
      toast({
        title: "No Valid Invoices",
        description: errorMsg,
        variant: "destructive"
      });
      return;
    }

    if (!uploadedFile) {
      const errorMsg = "No file available for processing";
      toast({
        title: "File Error",
        description: errorMsg,
        variant: "destructive"
      });
      return;
    }

    if (!batchName || !batchName.trim()) {
      const errorMsg = "Please provide a valid batch name";
      toast({
        title: "Invalid Batch Name",
        description: errorMsg,
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);

    try {
      const fileName = uploadedFile.name || '';
      const fileExtension = fileName.toLowerCase().split('.').pop() || 'csv';

      // Create a new batch object with all valid invoices
      const newBatch = {
        name: `${batchName}`,
        status: "draft",
        invoiceCount: validInvoices.length,
        totalAmount: validInvoices.reduce((sum, inv) => sum + (inv.total || 0), 0),
        createdAt: new Date().toISOString().split('T')[0],
        customFields: Object.keys(validInvoices.length > 0 ? validInvoices[0].customFields || {} : {})
      };
      console.log(validInvoices)
      onBatchCreate(uploadedFile, validInvoices, fileExtension, batchName.trim(), newBatch);

      // Reset form and hide preview
      resetForm();
    } catch (error) {
      const errorMsg = `Failed to process file import: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error("File import error:", error);
      toast({
        title: "Import Failed",
        description: errorMsg,
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const resetForm = () => {
    setShowPreview(false);
    setCsvData([]);
    setCsvHeaders([]);
    setValidInvoices([]);
    setInvalidInvoices([]);
    setValidationErrors([]);
    setInvoiceValidationMap({});
    setUploadedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const cancelPreview = () => {
    resetForm();
  };

  return showPreview ?
    (
      <InvoicePreviewCard
        csvHeaders={csvHeaders}
        csvData={csvData}
        validInvoices={validInvoices}
        invalidInvoices={invalidInvoices}
        invoiceValidationMap={invoiceValidationMap}
        isProcessing={isProcessing}
        onCancel={cancelPreview}
        onProcess={processCsvImport}
      />
    ) : (
      <div className={className}>
        <div className="space-y-4">
          {/* Customer Selection */}
          <CustomerSelector
            customers={customers}
            selectedCustomer={selectedCustomer}
            onCustomerChange={onCustomerChange}
            label="Select Customer for File Import"
            placeholder="Choose a customer to import invoices for..."
            required={true}
            showDetails={true}
          />

          {/* CSV Upload Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                File Import (CSV/Excel)
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="file-upload">Upload CSV or Excel File</Label>
                <Input
                  id="file-upload"
                  type="file"
                  accept=".csv,.xlsx,.xls"
                  ref={fileInputRef}
                  onChange={handleFileUpload}
                  disabled={!selectedCustomer}
                  className="mt-2"
                />
                <p className="text-sm text-muted-foreground mt-1">
                  {!selectedCustomer
                    ? "Please select a customer first before uploading file"
                    : `Supported formats: CSV, Excel (.xlsx, .xls). Required columns: ${DEFAULT_REQUIRED_COLUMNS.join(', ')}`
                  }
                </p>
              <div className="mt-2 p-3 bg-muted/10 rounded-md">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <p className="text-xs text-muted-foreground mb-2">
                      <strong>Sample format (CSV/Excel):</strong>
                    </p>
                    <code className="text-xs bg-muted px-2 py-1 rounded">
                      {/*DEFAULT_REQUIRED_COLUMNS.join(',')*/ "In construction..."}
                    </code>
                    <p className="text-xs text-muted-foreground mt-2">
                      <strong>Excel requirements:</strong> Data should start from row 1 with headers in the first row.
                      Only the first worksheet will be processed. Empty rows will be ignored.
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadSampleCSV}
                    className="ml-2 flex items-center gap-1"
                    disabled={true}
                  >
                    <Download className="h-3 w-3" />
                    CSV Template
                  </Button>
                </div>
              </div>
            </div>

            {validationErrors.length > 0 && (
              <div className="border border-destructive/20 bg-destructive/10 rounded-md p-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="h-4 w-4 text-destructive" />
                  <span className="font-medium text-destructive">Validation Errors</span>
                </div>
                <ul className="text-sm text-destructive space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            {csvData.length > 0 && validationErrors.length === 0 && (
              <div className="border border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950 rounded-md p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <span className="font-medium text-green-800 dark:text-green-200">Ready to Import</span>
                </div>
                <p className="text-sm text-green-700 dark:text-green-300">
                  {csvData.length} invoices ready for import
                </p>
              </div>
            )}
            </CardContent>
          </Card>
        </div>
      </div>
    );
}
