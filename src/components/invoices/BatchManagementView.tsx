import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";

import { Button } from "@/components/ui/button"; 
import { AlertCircle, ExternalLink } from "lucide-react";

import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { CSVImportTab } from "./CSVImportTab";
import { CreateBatchTab } from "./CreateBatchTab";
import { ManageBatchesTab } from "./ManageBatchesTab";
import { ExportBatchTab, type ExportConfig } from "./ExportBatchTab";
import { BatchDetailsDialog } from "./";
import { postToApi, getFromApi, patchToApi, uploadGivenFile } from '@/lib/api';
import { IInvoiceRes, IBatchResponse } from '@/types/invoices';
import { ICustomerRes } from '@/types';

export interface INewInvoice {
  customerName: string;
  email: string;
  phone: string;
  country: string;
  province: string;
  city: string;
  address: string;
  notes: string;
  tax: number;
  subTotal: number;
  total: number;
  dueDate: string;
  status: 'draft' | 'sent' | 'paid';
  customFields: Record<string, any>;
}

export interface INewBatch {
  name: string;
  status: 'draft' | 'sent' | 'paid';
  invoiceCount: number;
  totalAmount: number;
  createdAt: string;
  customFields: Array<string>;
}

// Mock data with sample invoices

interface IBatchManagementTabProps {
  user: any;
  provider: any;
  invoices: IInvoiceRes[];
  customers: any[];
  fetchInvoices: () => void;
}

export function BatchManagementTab({
  user,
  provider,
  invoices,
  customers,
  fetchInvoices
}: IBatchManagementTabProps) {
  const [activeSubTab, setActiveSubTab] = useState("import");
  const [batches, setBatches] = useState<IBatchResponse[]>([]);
  const [selectedCustomer, setSelectedCustomer] = useState<ICustomerRes | null>(null);
  const [selectedBatch, setSelectedBatch] = useState<IBatchResponse | null>(null);
  const [isBatchDetailsOpen, setIsBatchDetailsOpen] = useState(false);
  const [isInfoDialogOpen, setIsInfoDialogOpen] = useState(false);
  const [exportResultData, setExportResultData] = useState(null);
  const { toast } = useToast();

  useEffect(() => {
    if (provider) {
      fetchBatches();
    }
  }, [provider, user]);

  const fetchBatches = () => {
    if (provider) {
      let url = `/api/v1/provider/${provider.id}/invoice_batches/90`;
      getFromApi(url, user, (response) => {
        console.log("BatchManagementTab useEffect", response.result);
        let batches = response.result || [];
        setBatches(batches.sort((a, b) => a.id - b.id));
      },
        (error) => {
          console.error("Error fetching batches:", error);
        });
    }
  };

  const createBatchFromCSV = (file: File, newInvoices: INewInvoice[], fileExtension: string, batchName?: string, newBatch?: any) => {
    if (fileExtension === "xlsx" || fileExtension === "xls") {
      return alert("Excel import is not supported yet. Please use CSV format.");
    }
    const finalBatchName = batchName || `CSV Import ${new Date().toLocaleDateString()}`;

    //TODO: Api not working - when API is ready, include batch name in the request
    const url = `/api/v1/invoice/batch/import/${provider.id}/${selectedCustomer?.id}/${fileExtension}`;
    console.log(url, "Batch name:", finalBatchName)
    uploadGivenFile("POST", url, user, {}, 'batch_invoice', file,
      (onProgressRes) => {
        console.log("onProgressRes:", onProgressRes);
      },
      (onLoadRes) => {
        console.log("onLoadRes:", onLoadRes);
      },
      (response) => {
        console.log("response:", response)
        toast({
          title: "Batch Created",
          description: `Created batch "${finalBatchName}"`,
        });
        fetchInvoices();
      },
      (error) => {
        // console.error("Error creating batch:", error);
        // toast({
        //   title: "Batch Creation Failed",
        //   description: "Could not create batch",
        //   variant: "destructive"
        // });

        const newBatchWithId: IBatchResponse = {
          id: batches.length + 1,
          name: newBatch.name,
          status: newBatch.status,
          invoiceCount: newBatch.invoiceCount,
          totalAmount: newBatch.totalAmount,
          createdAt: newBatch.createdAt,
          customFields: newBatch.customFields,
          invoices: newInvoices as any
        };
        console.log("newBatchWithId:", newBatchWithId)
        setBatches(prev => [...prev, newBatchWithId]);
        toast({
          title: "Batch Created",
          description: `Created batch "${batchName}" with ${newInvoices.length} invoices`,
        });
      }
    );


    // Functional code in local
    // const newBatchWithId: IBatchResponse = {
    //   id: batches.length + 1,
    //   name: batch.name,
    //   status: batch.status,
    //   invoiceCount: batch.invoiceCount,
    //   totalAmount: batch.totalAmount,
    //   createdAt: batch.createdAt,
    //   customFields: batch.customFields,
    //   invoices: newInvoices as any
    // };
    // console.log("newBatchWithId:", newBatchWithId)
    // setBatches(prev => [...prev, newBatchWithId]);
    // toast({
    //   title: "Batch Created",
    //   description: `Created batch "${batch.name}" with ${newInvoices.length} invoices`,
    // });

  };

  const createBatchFromInvoices = (batch: INewBatch, selectedInvoices: IInvoiceRes[]) => {
    const url = `/api/v1/invoice/batch `;
    // postToApi(url, user, batch,
    //   (response) => {
    //     console.log("batch response:", response);

    //     Promise.all(selectedInvoices.map((invoice) => {
    //       const invoiceUrl = `/api/v1/invoice/batch/${response.result.id}/add_invoice`; //TODO: check if this is correct
    //       return postToApi(invoiceUrl, user, invoice,
    //         (invoiceResponse) => {
    //           console.log("invoice response:", invoiceResponse);
    //         },
    //         (error) => {
    //           console.error("Error adding invoice to batch:", error);
    //         }
    //       );
    //     }))
    //   },
    //   (error) => {
    //     console.error("Error creating batch:", error);
    //   }
    // );

    const newBatchWithId: IBatchResponse = {
      id: batches.length + 1,
      name: batch.name,
      status: batch.status,
      invoiceCount: batch.invoiceCount,
      totalAmount: batch.totalAmount,
      createdAt: batch.createdAt,
      customFields: batch.customFields,
      invoices: selectedInvoices
    };
    setBatches(prev => [...prev, newBatchWithId]);
    toast({
      title: "Batch Created",
      description: `Created batch "${batch.name}" with ${selectedInvoices.length} invoices`,
    });

  }

  const updateBatchStatus = (batchId: number, newStatus: 'draft' | 'sent' | 'paid') => {
    const url = `/api/v1/invoice/batch/${batchId}/status`
    patchToApi(url, user, { status: newStatus },
      (response) => {
        console.log("Status response:", response)
        setBatches(prev => prev.map(batch =>
          batch.id === batchId
            ? { ...batch, status: newStatus }
            : batch
        ));
        newStatus != "sent" ?
          toast({
            title: "Status Updated",
            description: `Batch status updated to ${newStatus}`,
          }) : ""
      },
      (error) => {
        console.error("Error updating batch status:", error);
      }
    );

  };

  const sendBatchEmails = (batchId: number, batchName: string) => {
    const url = `/api/v1/invoice/batch/${batchId}/send `;
    postToApi(url, user, {}, (response) => {
      console.log("Email response:", response)
      updateBatchStatus(batchId, 'sent');
      toast({
        title: "Email Sent",
        description: `Batch "${batchName}" sent to customers`,
      });
    },
      (error) => {
        console.error("Error sending batch emails:", error);
      }
    );

  };

  const deleteBatch = (batchId: number, batchName: string) => {
    //TODO: its necessary to delete the batch?
    setBatches(prev => prev.filter(b => b.id !== batchId));
    toast({
      title: "Batch Deleted",
      description: `Batch "${batchName}" has been deleted`,
    });
  };

  const handleViewBatch = (batchId: number) => {
    const batch = batches.find(b => b.id === batchId);
    if (batch) {
      setSelectedBatch(batch);
      setIsBatchDetailsOpen(true);
    }
  };

  const handleExport = (config: ExportConfig) => {
    //TODO:
    console.log("Exporting batch with config:", config);
    toast({
      title: "Export Started",
      description: "Your batch export is being prepared for download",
    });

    let path = `/api/v1/invoice/batch/${config.batchId}/export/${config.format}`;
    getFromApi(path, user, (response) => {
      console.log("response:", response)
      toast({
        title: "Export Complete",
        description: "Your batch export is ready for download",
      });

      if (response.result) {
        setExportResultData(response.result );
        setIsInfoDialogOpen(true);
      }
    },
      (error) => {
        console.error("Error exporting batch:", error);
      }
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Batch Management</h2>
          <p className="text-muted-foreground">Import, create, and manage invoice batches</p>
        </div>
      </div>

      <Tabs value={activeSubTab} onValueChange={setActiveSubTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="import">CSV Import</TabsTrigger>
          <TabsTrigger value="create">Create Batch</TabsTrigger>
          <TabsTrigger value="manage">Manage Batches</TabsTrigger>
          <TabsTrigger value="export">Export</TabsTrigger>
        </TabsList>

        <TabsContent value="import" className="space-y-4">
          <CSVImportTab
            customers={customers}
            selectedCustomer={selectedCustomer}
            onCustomerChange={setSelectedCustomer}
            onBatchCreate={createBatchFromCSV}
          />
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <CreateBatchTab
            invoices={invoices}
            customers={customers}
            selectedCustomer={selectedCustomer}
            onCustomerChange={setSelectedCustomer}
            onBatchCreate={createBatchFromInvoices}
            onSuccess={(message) => toast({ title: "Success", description: message })}
            onError={(message) => toast({ title: "Error", description: message, variant: "destructive" })}
          />
        </TabsContent>

        <TabsContent value="manage" className="space-y-4">
          <ManageBatchesTab
            batches={batches}
            onStatusUpdate={updateBatchStatus}
            onSendEmails={sendBatchEmails}
            onDeleteBatch={deleteBatch}
            onViewBatch={handleViewBatch}
            onSuccess={(message) => toast({ title: "Success", description: message })}
            onError={(message) => toast({ title: "Error", description: message, variant: "destructive" })}
          />
        </TabsContent>

        <TabsContent value="export" className="space-y-4">
          <ExportBatchTab
            user={user}
            batches={batches}
          />
        </TabsContent>
      </Tabs>

      <BatchDetailsDialog
        isOpen={isBatchDetailsOpen}
        onOpenChange={setIsBatchDetailsOpen}
        batch={selectedBatch}
      />

      {/* Result info Dialog */}
      <Dialog open={isInfoDialogOpen} onOpenChange={setIsInfoDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Export Results
            </DialogTitle>
          </DialogHeader>

          {exportResultData && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-green-50 p-3 rounded-md border border-green-200">
                  <p className="text-sm font-medium text-green-800">Successfully Exported Invoice Batch</p>
                  <p className="text-2xl font-bold text-green-700"># {exportResultData.batchNumber}</p>
                </div>

              </div>

              <div className="bg-muted p-3 rounded-md">
                <p className="text-sm font-medium mb-2">Export Download</p>
                <div className="flex items-center gap-2">
                  <ExternalLink className="h-4 w-4 text-muted-foreground" />
                  <a
                    href={exportResultData.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    Download your exported invoice batch
                  </a>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button onClick={() => setIsInfoDialogOpen(false)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
