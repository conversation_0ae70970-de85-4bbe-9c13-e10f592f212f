// Removed Card components - now using full screen layout
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Eye, X, ArrowLeft, Upload } from "lucide-react";
import { INewInvoice } from "./BatchManagementView";
import { useState, useEffect } from "react";

interface InvoicePreviewCardProps {
  csvHeaders: string[];
  csvData: any[];
  validInvoices: INewInvoice[];
  invalidInvoices: INewInvoice[];
  invoiceValidationMap: Record<number, string[]>;
  isProcessing: boolean;
  onCancel: () => void;
  onProcess: (batchName: string) => void;
}

export function InvoicePreviewCard({
  csvHeaders,
  validInvoices,
  invalidInvoices,
  invoiceValidationMap,
  isProcessing,
  onCancel,
  onProcess
}: InvoicePreviewCardProps) {
  const [batchName, setBatchName] = useState<string>('');
  const [batchNameError, setBatchNameError] = useState<string>('');

  const generateDefaultBatchName = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `Import-Batch_${year}-${month}-${day}`;
  };

  useEffect(() => {
    setBatchName(generateDefaultBatchName());
  }, []);

  const validateBatchName = (name: string): boolean => {
    const trimmedName = name.trim();
    if (!trimmedName) {
      setBatchNameError('Batch name cannot be empty');
      return false;
    }
    if (trimmedName.length < 3) {
      setBatchNameError('Batch name must be at least 3 characters long');
      return false;
    }
    if (trimmedName.length > 50) {
      setBatchNameError('Batch name cannot exceed 50 characters');
      return false;
    }
    setBatchNameError('');
    return true;
  };

  const handleBatchNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    setBatchName(newName);
    if (!batchNameError) {
      validateBatchName(newName);
    }
  };

  const handleProcessBatch = () => {
    if (validateBatchName(batchName)) {
      onProcess(batchName.trim());
    }
  };

  const formatFieldName = (fieldKey: string) => {
    return fieldKey
      .split(/(?=[A-Z])|_|-/) // Split on camelCase, underscores, or hyphens
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };
  return (
    <div className="h-screen w-full flex flex-col bg-background">
      {/* Fixed Header */}
      <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex justify-between items-center p-4 sm:p-6">
          <div className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            <h2 className="text-lg sm:text-xl font-semibold">Invoice Preview</h2>
          </div>
          <Button variant="ghost" size="sm" onClick={onCancel} className="h-8 w-8 p-0">
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Fixed Summary Bar with Batch Name Input */}
      <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center p-4 gap-4">
          <div className="flex items-center gap-2 sm:gap-4 flex-wrap">
            <Badge variant="outline" className="px-2 py-1 text-xs">
              Total: {validInvoices.length + invalidInvoices.length} invoices
            </Badge>
            <Badge variant="secondary" className="px-2 py-1 text-xs">
              Valid: {validInvoices.length}
            </Badge>
            {Object.keys(invoiceValidationMap).length > 0 && (
              <Badge variant="destructive" className="px-2 py-1 text-xs">
                Errors: {Object.keys(invoiceValidationMap).length}
              </Badge>
            )}
          </div>
          <div className="flex flex-col gap-1">
            <div>
              <Input
                type="text"
                placeholder="Batch name..."
                value={batchName}
                onChange={handleBatchNameChange}
                className={`min-w-[200px] ${batchNameError ? 'border-destructive' : ''}`}
                onBlur={() => validateBatchName(batchName)}
              />
            </div>
            {batchNameError && (
              <span className="text-xs text-destructive">{batchNameError}</span>
            )}
          </div>
        </div>
      </div>

      {/* Scrollable Table Container */}
      <div className="flex-1 overflow-auto border-x bg-background">
        <table className="w-full caption-bottom text-sm">
          <thead className="sticky top-0 z-10 bg-background border-b [&_tr]:border-b">
            <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
              {
                csvHeaders.map(fieldKey => (
                  <th key={fieldKey} className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[120px] whitespace-nowrap">
                    {formatFieldName(fieldKey)}
                  </th>
                ))
              }
              <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[100px]">Status</th>
            </tr>
          </thead>
          <tbody className="[&_tr:last-child]:border-0">
            {[...validInvoices, ...invalidInvoices].map((invoice, index) => {
              const isInvalid = index >= validInvoices.length;
              const hasErrors = !!invoiceValidationMap[index];

              return (
                <tr
                  key={index}
                  className={`border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted ${isInvalid || hasErrors ? "bg-destructive/10" : ""}`}
                >
                  {csvHeaders.length > 0 ? (
                    csvHeaders.map(fieldKey => (
                      <td key={fieldKey} className="p-4 align-middle max-w-[200px] truncate whitespace-nowrap">
                        <div className="truncate" title={String(invoice[fieldKey] ?? (invoice.customFields?.[fieldKey] ?? ''))}>
                          {invoice[fieldKey] ?? (invoice.customFields?.[fieldKey] ?? '-')}
                        </div>
                      </td>
                    ))
                  ) : (
                    <td className="p-4 align-middle text-muted-foreground italic text-center">
                      -
                    </td>
                  )}
                  <td className="p-4 align-middle min-w-[100px]">
                    {isInvalid || hasErrors ? (
                      <div className="group relative">
                        <Badge variant="destructive">Error</Badge>
                        <div className="absolute z-50 invisible group-hover:visible bg-white dark:bg-gray-800 shadow-lg rounded-md p-2 w-64 text-xs border mt-1 right-0">
                          <ul className="list-disc pl-4 space-y-1">
                            {invoiceValidationMap[index]?.map((error, i) => (
                              <li key={i}>{error}</li>
                            )) || <li>Invalid invoice format</li>}
                          </ul>
                        </div>
                      </div>
                    ) : (
                      <Badge variant="outline">Valid</Badge>
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>

      {/* Fixed Footer */}
      <div className="flex-shrink-0 border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex flex-col sm:flex-row sm:justify-between items-stretch sm:items-center p-4 gap-3">
          <Button
            variant="outline"
            className="flex items-center justify-center gap-2 order-2 sm:order-1"
            onClick={onCancel}
          >
            <ArrowLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Back to Upload</span>
            <span className="sm:hidden">Back</span>
          </Button>
          <Button
            className="flex items-center justify-center gap-2 order-1 sm:order-2"
            onClick={handleProcessBatch}
            disabled={validInvoices.length === 0 || isProcessing || !!batchNameError || !batchName.trim()}
          >
            {isProcessing ? (
              <>Processing...</>
            ) : (
              <>
                <Upload className="h-4 w-4" />
                <span className="hidden sm:inline">Process {validInvoices.length} Valid Invoices</span>
                <span className="sm:hidden">Process ({validInvoices.length})</span>
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
