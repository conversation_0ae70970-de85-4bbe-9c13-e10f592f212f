import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MultiSelect } from "@/components/ui/multi-select";
import { RotateCcw, Calculator } from "lucide-react";
import { ICustomField } from '@/types';

// Constants for dropdown options
export const INVOICE_STATUS_OPTIONS = [
  { label: "Draft", value: "draft" },
  { label: "Sent", value: "sent" },
  { label: "Paid", value: "paid" },
  { label: "Overdue", value: "overdue" }
];

export const PAYMENT_METHOD_OPTIONS = [
  { label: "Cash", value: "cash" },
  { label: "Check", value: "check" },
  { label: "Credit Card", value: "credit_card" },
  { label: "Bank Transfer", value: "bank_transfer" },
  { label: "Other", value: "other" }
];

export const FLAG_OPTIONS = [
  { label: "Urgent", value: "urgent" },
  { label: "Recurring", value: "recurring" },
  { label: "Created by Provider", value: "createdByProvider" },
  { label: "Paid", value: "paid" }
];

interface ManualInvoiceDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  manualInvoice: any;
  setManualInvoice: (invoice: any) => void;
  customers: any[];
  onCustomerChange: (customerId: string) => void;
  customFields: ICustomField[];
  setCustomFields: (fields: ICustomField[]) => void;
  onCreateInvoice: () => void;
  addInvoiceItem: () => void;
  removeInvoiceItem: (index: number) => void;
  updateInvoiceItem: (index: number, field: string, value: string) => void;
  calculateSubtotal: () => number;
  calculateTax: () => number;
  calculateTotal: () => number;
  getDisplaySubtotal: () => number;
  getDisplayTax: () => number;
  getDisplayTotal: () => number;
  handleSubtotalOverride: (value: string) => void;
  handleTaxOverride: (value: string) => void;
  handleTotalOverride: (value: string) => void;
  resetToCalculated: () => void;
}

export function ManualInvoiceDialog({
  isOpen,
  onOpenChange,
  manualInvoice,
  setManualInvoice,
  customers,
  onCustomerChange,
  customFields,
  setCustomFields,
  onCreateInvoice,
  addInvoiceItem,
  removeInvoiceItem,
  updateInvoiceItem,
  calculateSubtotal,
  calculateTax,
  calculateTotal,
  getDisplaySubtotal,
  getDisplayTax,
  getDisplayTotal,
  handleSubtotalOverride,
  handleTaxOverride,
  handleTotalOverride,
  resetToCalculated,
}: ManualInvoiceDialogProps) {

  const handleDateChange = (dateValue: string) => {
    if (!dateValue) {
      setManualInvoice({ ...manualInvoice, dueDate: "" });
      return;
    }
    const date = new Date(dateValue + 'T12:00:00.000Z');
    const isoString = date.toISOString();

    setManualInvoice({ ...manualInvoice, dueDate: isoString });
  };

  // Helper function to get the date value for the input field (YYYY-MM-DD format)
  const getDateInputValue = () => {
    if (!manualInvoice.dueDate) return "";

    try {
      const date = new Date(manualInvoice.dueDate);
      
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error("Error parsing date:", error);
      return "";
    }
  };

  const getCustomFieldValue = (label: string) => {
    const field = customFields.find(field => field.label === label);
    return field?.value || '';
  };

  const handleCustomFieldChange = (label: string, value: any) => {
    setCustomFields((prev) => 
      prev.map(field => 
        field.label === label 
          ? { ...field, value } 
          : field
      )
    );
  };

  const renderCustomField = (field: ICustomField) => {
    const fieldId = `${'business'}_${field.label.replace(/\s+/g, '_').toLowerCase()}`;
    const value = getCustomFieldValue(field.label);

    switch (field.type) {
      case 'text':
        return (
          <Input
            id={fieldId}
            type="text"
            value={value}
            onChange={(e) => handleCustomFieldChange(field.label, e.target.value, )}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            required={field.required}
          />
        );

      case 'number':
        return (
          <Input
            id={fieldId}
            type="number"
            value={value}
            onChange={(e) => handleCustomFieldChange(field.label, e.target.value)}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            required={field.required}
          />
        );

      case 'date':
        return (
          <Input
            id={fieldId}
            type="date"
            value={value}
            onChange={(e) => handleCustomFieldChange(field.label, e.target.value)}
            required={field.required}
          />
        );

      case 'select':
        return (
          <Select
            value={value}
            onValueChange={(selectedValue) => handleCustomFieldChange(field.label, selectedValue)}
          >
            <SelectTrigger id={fieldId}>
              <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options.map((option) => (
                <SelectItem key={option.value} value={option.value.toString()}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'textarea':
        return (
          <Textarea
            id={fieldId}
            value={value}
            onChange={(e) => handleCustomFieldChange(field.label, e.target.value)}
            placeholder={field.placeholder || `Enter ${field.label.toLowerCase()}`}
            required={field.required}
          />
        );

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <input
              id={fieldId}
              type="checkbox"
              checked={value === true || value === 'true'}
              onChange={(e) => handleCustomFieldChange(field.label, e.target.checked)}
              className="rounded border-gray-300"
            />
            <Label htmlFor={fieldId} className="text-sm font-normal">
              {field.placeholder || field.label}
            </Label>
          </div>
        );

      default:
        return null;
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Manual Invoice</DialogTitle>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Customer Selection */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="customer" className="text-right">
              Customer*
            </Label>
            <div className="col-span-3">
              <Select
                value={manualInvoice.customerId}
                onValueChange={onCustomerChange}
              >
                <SelectTrigger id="customer">
                  <SelectValue placeholder="Select customer" />
                </SelectTrigger>
                <SelectContent>
                  {customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id.toString()}>
                      {customer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Location Fields */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="country" className="text-right">
              Country
            </Label>
            <div className="col-span-3">
              <Input
                id="country"
                value={manualInvoice.country}
                onChange={(e) => setManualInvoice({ ...manualInvoice, country: e.target.value })}
                placeholder="Country"
              />
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="province" className="text-right">
              Province/State
            </Label>
            <div className="col-span-3">
              <Input
                id="province"
                value={manualInvoice.province}
                onChange={(e) => setManualInvoice({ ...manualInvoice, province: e.target.value })}
                placeholder="Province or State"
              />
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="city" className="text-right">
              City
            </Label>
            <div className="col-span-3">
              <Input
                id="city"
                value={manualInvoice.city}
                onChange={(e) => setManualInvoice({ ...manualInvoice, city: e.target.value })}
                placeholder="City"
              />
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="address" className="text-right">
              Address
            </Label>
            <div className="col-span-3">
              <Input
                id="address"
                value={manualInvoice.address}
                onChange={(e) => setManualInvoice({ ...manualInvoice, address: e.target.value })}
                placeholder="Street address"
              />
            </div>
          </div>

          {/* Contact Fields */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              Email
            </Label>
            <div className="col-span-3">
              <Input
                id="email"
                type="email"
                value={manualInvoice.email}
                onChange={(e) => setManualInvoice({ ...manualInvoice, email: e.target.value })}
                placeholder="<EMAIL>"
              />
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="phone" className="text-right">
              Phone
            </Label>
            <div className="col-span-3">
              <Input
                id="phone"
                type="tel"
                value={manualInvoice.phone}
                onChange={(e) => setManualInvoice({ ...manualInvoice, phone: e.target.value })}
                placeholder="Phone number"
              />
            </div>
          </div>

          {/* Status Dropdown */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Status
            </Label>
            <div className="col-span-3">
              <Select
                value={manualInvoice.status}
                onValueChange={(value) => setManualInvoice({ ...manualInvoice, status: value })}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {INVOICE_STATUS_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Payment Method Dropdown */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="paymentMethod" className="text-right">
              Payment Method
            </Label>
            <div className="col-span-3">
              <Select
                value={manualInvoice.paymentMethod}
                onValueChange={(value) => setManualInvoice({ ...manualInvoice, paymentMethod: value })}
              >
                <SelectTrigger id="paymentMethod">
                  <SelectValue placeholder="Select payment method" />
                </SelectTrigger>
                <SelectContent>
                  {PAYMENT_METHOD_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Flags Multi-Select */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="flags" className="text-right">
              Flags
            </Label>
            <div className="col-span-3">
              <MultiSelect
                options={FLAG_OPTIONS}
                selected={manualInvoice.flags}
                onChange={(flags) => setManualInvoice({ ...manualInvoice, flags })}
                placeholder="Select flags..."
              />
            </div>
          </div>

          {/* Notes */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="notes" className="text-right">
              Notes
            </Label>
            <div className="col-span-3">
              <Textarea
                id="notes"
                placeholder="Add any additional notes"
                value={manualInvoice.notes}
                onChange={(e) => setManualInvoice({ ...manualInvoice, notes: e.target.value })}
              />
            </div>
          </div>

          {/* Due Date */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="dueDate" className="text-right">
              Due Date*
            </Label>
            <div className="col-span-3">
              <Input
                id="dueDate"
                type="date"
                value={getDateInputValue()}
                onChange={(e) => handleDateChange(e.target.value)}
              />
            </div>
          </div>

          {/* Tax Rate */}
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="taxRate" className="text-right">
              Tax Rate (%)
            </Label>
            <div className="col-span-3">
              <Input
                id="taxRate"
                type="number"
                placeholder="0"
                value={manualInvoice.taxRate}
                onChange={(e) => setManualInvoice({ ...manualInvoice, taxRate: e.target.value })}
              />
            </div>
          </div>

          <h1><b>Custom Fields</b></h1>
          {customFields.map((field) => (
            <div key={field.label} className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor={field.label} className="text-right">
                {field.label}
              </Label>
              <div className="col-span-3">
                {renderCustomField(field)}
              </div>
            </div>
          ))}

          {/* Invoice Items */}
          <div className="mt-4">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-lg font-medium">Invoice Items</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addInvoiceItem}
              >
                Add Item
              </Button>
            </div>

            <div className="space-y-4">
              {manualInvoice.items.map((item: any, index: number) => (
                <div key={index} className="grid grid-cols-12 gap-2 items-start border p-3 rounded-md">
                  <div className="col-span-6">
                    <Label htmlFor={`item-desc-${index}`} className="mb-1 block">Description</Label>
                    <Input
                      id={`item-desc-${index}`}
                      value={item.description}
                      onChange={(e) => updateInvoiceItem(index, 'description', e.target.value)}
                      placeholder="Item description"
                    />
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor={`item-qty-${index}`} className="mb-1 block">Quantity</Label>
                    <Input
                      id={`item-qty-${index}`}
                      type="number"
                      value={item.quantity}
                      onChange={(e) => updateInvoiceItem(index, 'quantity', e.target.value)}
                      placeholder="1"
                    />
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor={`item-price-${index}`} className="mb-1 block">Unit Price</Label>
                    <Input
                      id={`item-price-${index}`}
                      type="number"
                      value={item.unitPrice}
                      onChange={(e) => updateInvoiceItem(index, 'unitPrice', e.target.value)}
                      placeholder="0.00"
                    />
                  </div>
                  <div className="col-span-2">
                    <Label htmlFor={`item-total-${index}`} className="mb-1 block">Total</Label>
                    <Input
                      id={`item-total-${index}`}
                      type="number"
                      value={(parseFloat(item.quantity) * parseFloat(item.unitPrice)).toFixed(2)}
                      disabled
                    />
                  </div>
                  <div className="col-span-1">
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      onClick={() => removeInvoiceItem(index)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M18 6L6 18M6 6l12 12" /></svg>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Subtotal, Tax, and Total with Manual Override */}
          <div className="mt-6 space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Invoice Totals</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={resetToCalculated}
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Reset to Calculated
              </Button>
            </div>

            {/* Subtotal */}
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="col-span-2 flex items-center gap-2">
                <Label className="text-right">Subtotal</Label>
                {manualInvoice.isSubtotalOverridden && (
                  <Calculator className="h-4 w-4 text-override-icon" />
                )}
              </div>
              <div className="col-span-2">
                <Input
                  type="number"
                  step="0.01"
                  value={getDisplaySubtotal().toFixed(2)}
                  onChange={(e) => handleSubtotalOverride(e.target.value)}
                  className={manualInvoice.isSubtotalOverridden ? "border-override-border bg-override-background" : ""}
                  placeholder={calculateSubtotal().toFixed(2)}
                />
              </div>
            </div>

            {/* Tax */}
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="col-span-2 flex items-center gap-2">
                <Label className="text-right">Tax</Label>
                {manualInvoice.isTaxOverridden && (
                  <Calculator className="h-4 w-4 text-override-icon" />
                )}
              </div>
              <div className="col-span-2">
                <Input
                  type="number"
                  step="0.01"
                  value={getDisplayTax().toFixed(2)}
                  onChange={(e) => handleTaxOverride(e.target.value)}
                  className={manualInvoice.isTaxOverridden ? "border-override-border bg-override-background" : ""}
                  placeholder={calculateTax().toFixed(2)}
                />
              </div>
            </div>

            {/* Total */}
            <div className="grid grid-cols-4 items-center gap-4">
              <div className="col-span-2 flex items-center gap-2">
                <Label className="text-right font-semibold">Total</Label>
                {manualInvoice.isTotalOverridden && (
                  <Calculator className="h-4 w-4 text-override-icon" />
                )}
              </div>
              <div className="col-span-2">
                <Input
                  type="number"
                  step="0.01"
                  value={getDisplayTotal().toFixed(2)}
                  onChange={(e) => handleTotalOverride(e.target.value)}
                  className={`font-semibold ${manualInvoice.isTotalOverridden ? "border-override-border bg-override-background" : ""}`}
                  placeholder={calculateTotal().toFixed(2)}
                />
              </div>
            </div>

            {/* Visual indicator for manual overrides */}
            {(manualInvoice.isSubtotalOverridden || manualInvoice.isTaxOverridden || manualInvoice.isTotalOverridden) && (
              <div className="text-sm text-override-alert-foreground bg-override-alert-background p-3 rounded-md border border-override-alert-border">
                <div className="flex items-center gap-2">
                  <Calculator className="h-4 w-4" />
                  <span className="font-medium">Manual Override Active</span>
                </div>
                <p className="mt-1">
                  One or more values have been manually overridden. Use "Reset to Calculated" to return to automatic calculation.
                </p>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={onCreateInvoice}>
            Create Invoice
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
