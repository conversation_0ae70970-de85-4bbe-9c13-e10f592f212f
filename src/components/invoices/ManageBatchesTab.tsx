import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Mail,
  FileSpreadsheet,
  Trash2,
  Eye
} from "lucide-react";
import { IBatchResponse } from '@/types/invoices';

interface ManageBatchesTabProps {
  batches: IBatchResponse[];
  onStatusUpdate: (batchId: number, newStatus: 'draft' | 'sent' | 'paid') => void;
  onSendEmails: (batchId: number, batchName: string) => void;
  onDeleteBatch: (batchId: number, batchName: string) => void;
  onViewBatch?: (batchId: number) => void;
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
  className?: string;
}

export function ManageBatchesTab({
  batches,
  onStatusUpdate,
  onSendEmails,
  onDeleteBatch,
  onViewBatch,
  onSuccess,
  onError,
  className = ""
}: ManageBatchesTabProps) {

  const handleStatusUpdate = (batchId: number, newStatus: 'draft' | 'sent' | 'paid') => {
    try {
      onStatusUpdate(batchId, newStatus);
      onSuccess?.(`Batch status updated to ${newStatus}`);
    } catch (error) {
      onError?.("Failed to update batch status");
    }
  };

  const handleSendEmails = (batchId: number, batchName: string) => {
    try {
      onSendEmails(batchId, batchName);
      onSuccess?.(`Batch "${batchName}" sent to customers`);
    } catch (error) {
      onError?.("Failed to send batch emails");
    }
  };

  const handleDeleteBatch = (batchId: number, batchName: string) => {
    try {
      onDeleteBatch(batchId, batchName);
      onSuccess?.(`Batch "${batchName}" has been deleted`);
    } catch (error) {
      onError?.("Failed to delete batch");
    }
  };

  const handleViewBatch = (batchId: number) => {
    if (onViewBatch) {
      onViewBatch(batchId);
    }
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Manage Batches
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {batches.length === 0 ? (
              <div className="text-center py-8">
                <FileSpreadsheet className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Batches Found</h3>
                <p className="text-muted-foreground">
                  Create your first batch by importing a CSV or selecting existing invoices.
                </p>
              </div>
            ) : (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Batch #</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Invoices</TableHead>
                      <TableHead>Total Amount</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {batches.map((batch) => (
                      <TableRow key={batch.id}>
                        <TableCell className="font-medium"># {batch.num}</TableCell>
                        <TableCell>
                          <Badge variant={
                            batch.status === 'sent' ? 'default' :
                            batch.status === 'draft' ? 'secondary' : 'outline'
                          }>
                            {batch.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{batch.Invoices.length}</TableCell>
                        <TableCell>${batch.totalValue.toFixed(2)}</TableCell>
                        <TableCell>{new Date(batch.CreatedAt).toLocaleDateString()}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button 
                              variant="ghost" 
                              size="icon" 
                              className="h-8 w-8"
                              onClick={() => handleViewBatch(batch.id)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Select
                              value={batch.status}
                              onValueChange={(value: 'draft' | 'sent' | 'paid') =>
                                handleStatusUpdate(batch.id, value)
                              }
                            >
                              <SelectTrigger className="w-20 h-8">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="draft">Draft</SelectItem>
                                <SelectItem value="sent">Sent</SelectItem>
                                <SelectItem value="paid">Paid</SelectItem>
                              </SelectContent>
                            </Select>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => handleSendEmails(batch.id, batch.name)}
                              disabled={false} //TODO: should be disabled if batch is sent or paid??
                            >
                              <Mail className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-destructive"
                              onClick={() => handleDeleteBatch(batch.id, batch.name)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}