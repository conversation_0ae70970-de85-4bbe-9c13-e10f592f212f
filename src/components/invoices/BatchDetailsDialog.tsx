
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CalendarDays, FileSpreadsheet, DollarSign, Users } from "lucide-react";
import { IBatchResponse } from '@/types/invoices';

interface BatchDetailsDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  batch: IBatchResponse | null;
}

export function BatchDetailsDialog({
  isOpen,
  onOpenChange,
  batch
}: BatchDetailsDialogProps) {
  if (!batch) return null;

  const invoices = batch.invoices || [];
  const customFieldKeys = batch.customFields || [];

  const getCustomFieldValue = (invoice: any, fieldKey: string): string => {
    return invoice.customFields?.[fieldKey] ?? '';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'paid':
        return 'default';
      case 'sent':
        return 'secondary';
      case 'draft':
        return 'outline';
      default:
        return 'outline';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[95vw] lg:max-w-[1200px] max-h-[90vh] overflow-y-auto overflow-x-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Batch Details: {batch.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 w-full overflow-x-hidden">
          {/* Batch Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 w-full">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <CalendarDays className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Created</p>
                    <p className="font-medium">{formatDate(batch.createdAt)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Invoices</p>
                    <p className="font-medium">{batch.invoiceCount}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Total Amount</p>
                    <p className="font-medium">{formatCurrency(batch.totalAmount)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2">
                  <FileSpreadsheet className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Status</p>
                    <Badge variant={getStatusBadgeVariant(batch.status)}>
                      {batch.status}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>



          {/* Invoices Table */}
          <Card className="w-full overflow-hidden">
            <CardHeader>
              <CardTitle className="text-lg">Invoices in Batch ({invoices.length})</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              {invoices.length === 0 ? (
                <div className="text-center py-8 px-6">
                  <FileSpreadsheet className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Invoices Found</h3>
                  <p className="text-muted-foreground">
                    This batch doesn't contain any invoice data.
                  </p>
                </div>
              ) : (
                <div className="flex flex-col h-full w-full">
                  {/* Scrollable Table Container with independent horizontal and vertical scrolling */}
                  <div className="flex-1 overflow-auto max-h-[400px] border-x bg-background">
                    <table className="w-full caption-bottom text-sm">
                      <thead className="sticky top-0 z-10 bg-background border-b [&_tr]:border-b">
                        <tr className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[120px] max-w-[150px] sticky left-0 bg-background z-20 border-r shadow-sm whitespace-nowrap">
                            <div className="truncate" title="Invoice Number">
                              Invoice #
                            </div>
                          </th>
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[100px] max-w-[120px] whitespace-nowrap">
                            <div className="truncate" title="Amount">
                              Amount
                            </div>
                          </th>
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[80px] max-w-[100px] whitespace-nowrap">
                            <div className="truncate" title="Status">
                              Status
                            </div>
                          </th>
                          <th className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[100px] max-w-[120px] whitespace-nowrap">
                            <div className="truncate" title="Created Date">
                              Created
                            </div>
                          </th>
                          {customFieldKeys.map((fieldKey: string) => {
                            const formattedLabel = fieldKey.replace(/([A-Z])/g, ' $1').trim();
                            return (
                              <th
                                key={fieldKey}
                                className="h-12 px-4 text-left align-middle font-medium text-muted-foreground min-w-[120px] max-w-[200px] capitalize whitespace-nowrap"
                              >
                                <div className="truncate" title={formattedLabel}>
                                  {formattedLabel}
                                </div>
                              </th>
                            );
                          })}
                        </tr>
                      </thead>
                      <tbody className="[&_tr:last-child]:border-0">
                        {invoices.map((invoice) => {
                          const customFieldValues = customFieldKeys.map((fieldKey: string) =>
                            getCustomFieldValue(invoice, fieldKey) || '-'
                          );

                          return (
                            <tr key={invoice.id} className="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                              <td className="p-4 align-middle font-medium min-w-[120px] max-w-[150px] sticky left-0 bg-background border-r shadow-sm whitespace-nowrap">
                                <div className="truncate" title={invoice.Num?.toString() || 'N/A'}>
                                  {invoice.Num || 'N/A'}
                                </div>
                              </td>
                              <td className="p-4 align-middle min-w-[100px] max-w-[120px] whitespace-nowrap">
                                <div className="truncate" title={formatCurrency(invoice.total)}>
                                  {formatCurrency(invoice.total)}
                                </div>
                              </td>
                              <td className="p-4 align-middle min-w-[80px] max-w-[100px] whitespace-nowrap">
                                <Badge variant={getStatusBadgeVariant(invoice.status)}>
                                  {invoice.status}
                                </Badge>
                              </td>
                              <td className="p-4 align-middle min-w-[100px] max-w-[120px] whitespace-nowrap">
                                <div className="truncate" title={(invoice.CreatedAt)}>
                                  {formatDate(invoice.CreatedAt)}
                                </div>
                              </td>
                              {/* Dynamic custom field values */}
                              {customFieldValues.map((value: string, index: number) => (
                                <td
                                  key={customFieldKeys[index]}
                                  className="p-4 align-middle min-w-[120px] max-w-[200px] whitespace-nowrap"
                                >
                                  <div className="truncate" title={value}>
                                    {value}
                                  </div>
                                </td>
                              ))}
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
