import { useState, useContext, useEffect } from 'react';
import { TeamContext } from "@/context/TeamContext";
import { ConfigContext } from "@/context/ConfigContext";
import { BusinessContext } from "@/context/BusinessContext";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Plus, Trash2, Users } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { TabsContent } from "@/components/ui/tabs";
import { TeamMembersTable } from '@/components/shared/tables/TeamMembersTable';
import { postToApi, deleteToApi } from "@/lib/api";
import { IWorkOrderRes, IBusinessTeamRes, ILocationTeamRes, ITeamMemberWorkOrderRes } from "@/types";
import { useToast } from '@/hooks/use-toast';

/**
 * Tab that displays the team members assigned to a work order.
 *
 * The tab displays a table of all the team members assigned to the work order.
 * Each row in the table displays the team member's name, role, and a button
 * to remove the team member from the work order.
 *
 * If no team members are assigned to the work order, the tab displays a message
 * indicating that no team members have been assigned.
 *
 * The tab also displays a button to add new team members to the work order. When
 * the button is clicked, a dialog is opened that allows the user to select the
 * team members to add to the work order.
 *
 * @param workOrder The work order to display the team members for.
 * @returns {JSX element} for the tab.
 */
interface TeamMembersTabProps {
  workOrder: IWorkOrderRes
  updateSelectedWorkOrder: () => void;
}
export function TeamMembersTab({ workOrder, updateSelectedWorkOrder }: TeamMembersTabProps): JSX.Element {
  const { user } = useContext(ConfigContext) as { user: any };
  const { provider, setupUser } = useContext(BusinessContext) as { provider: any, setupUser: any };
  const {
    businessTeam,
    locationTeam,
    getBusinessTeam,
    getLocationTeam,
  } = useContext(TeamContext) as { //TODO: correct types
    businessTeam: IBusinessTeamRes[],
    locationTeam: ILocationTeamRes[],
    getBusinessTeam: any,
    getLocationTeam: any,
    addTeamMember: any,
    deleteTeamMember: any
  }
  const { toast } = useToast();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [teamToDelete, setTeamToDelete] = useState<ITeamMemberWorkOrderRes | null>(null);

  useEffect(() => {
    setupUser(user);
    if (provider) {
      getBusinessTeam(user, provider, provider.locations[0]);
      getLocationTeam(user, provider.locations[0]);
    }
  }, [provider, user]);

  const assignMember = (team: IBusinessTeamRes) => {
    if(workOrder.TeamMembers?.some((m) => m.roleId == team.roleId)) {
      toast({
        title: "Member Already Assigned",
        description: `User ${team.Name} is already assigned to this work order`,
        variant: "destructive",
      });
      return;
    }

    const url = `/api/v1/work_order/${workOrder.id}/add_team_member`
    const data = {
      workOrderId: workOrder.id,
      roleId: team.roleId,
      businessRoleId: team.id
    }
    postToApi(url, user, data,
      (response) => {
        updateSelectedWorkOrder()
        setIsDialogOpen(false);

        toast({
          title: "Team Member Assigned",
          description: `Assigned ${team.Name} to the work order`,
        });
      }, (error) => {
        toast({
          title: "Error",
          description: `Could not assign team member: ${error}`,
          variant: "destructive",
        });
      }
    )
  }

  const handleDeleteMember = (team: ITeamMemberWorkOrderRes) => {
    setTeamToDelete(team);
    setIsDeleteDialogOpen(true);
  }

  const removeMember = (team: ITeamMemberWorkOrderRes) => {
    const url = `/api/v1/work_order/${workOrder.id}/team_member/${team.id}`
    const data = {}
    console.log("url: ", url)
    console.log("data: ", data)
    deleteToApi(url, user, data,
      (response) => {
        updateSelectedWorkOrder()
        setIsDeleteDialogOpen(false);
        toast({
          title: "Team Member Removed",
          description: `Removed ${team.user.name} from the work order`,
        });
      }, (error) => {
        toast({
          title: "Error",
          description: `Could not remove team member: ${error}`,
          variant: "destructive",
        });
      }
    )
  }

  return (
    <>
      <TabsContent value="team" className="space-y-4 mt-4">
        <Card>
          <CardContent className="pt-6">
            <div className='flex justify-between items-center pb-2'>
              <h3 className="text-lg font-medium mb-2">Assigned Team Members</h3>
              {!workOrder.TeamMembers || workOrder.TeamMembers.length != 0 && (
                <Button
                  onClick={() => setIsDialogOpen(true)}
                  className="btn btn-primary mt-4">
                  <Plus className="h-4 w-4" />Add Team Members
                </Button>
              )}
            </div>
            {workOrder.TeamMembers && workOrder.TeamMembers.length > 0 ? (
              <TeamMembersTable
                teams={workOrder.TeamMembers} //ITeamMemberWorkOrderRes[]
                mapper={(member) => ({
                  photo: member.user.photo,
                  name: member.user.name,
                  email: member.user.email,
                  roles: member.user.roles,
                  location: `${member.user.country}, ${member.user.province}, ${member.user.city}`,
                })}
                columns={[
                  { label: 'Photo', render: m => <img src={m.photo} className="w-10 h-10 rounded-full" /> },
                  { label: 'Name', render: m => m.name },
                  { label: 'Email', render: m => m.email },
                  { label: 'Roles', render: m => m.roles ?? 'N/A' },
                  { label: 'Location', render: m => m.location ?? 'N/A' },
                ]}
                actions={[{
                  action: handleDeleteMember,
                  label: <Trash2 className="h-4 w-4" />,
                  btnClassName: "h-8 w-8 text-destructive"
                }]}
              />
            ) : (
              <div className="text-center py-6">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No Team Members Assigned</h3>
                <p className="text-muted-foreground">
                  No technicians have been assigned to this work order.
                </p>
                <Button
                  onClick={() => setIsDialogOpen(true)}
                  className="btn btn-primary mt-4">Assign Team Members
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
      {isDialogOpen &&
        <AddTeamMembersDialog
          setIsDialogOpen={setIsDialogOpen}
          businessTeam={businessTeam}
          assignMember={assignMember}
        />
      }
      {isDeleteDialogOpen &&
        <DeleteTeamMemberDialog
          setIsDeleteDialogOpen={setIsDeleteDialogOpen}
          team={teamToDelete}
          removeMember={removeMember}
        />
      }
    </>
  )
}


/**
 * AddTeamMembersDialog displays a dialog that allows the user to assign technicians to a work order.
*
* @param {AddTeamMembersDialogProps} props
* @returns {JSX.Element}
*/
interface AddTeamMembersDialogProps {
  setIsDialogOpen: (value: boolean) => void,
  businessTeam: IBusinessTeamRes[],
  assignMember: (team: IBusinessTeamRes) => void
}
function AddTeamMembersDialog({ setIsDialogOpen, businessTeam, assignMember }: AddTeamMembersDialogProps): JSX.Element {
  return (
    <Dialog open={true} onOpenChange={setIsDialogOpen}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add Team Members</DialogTitle>
        </DialogHeader>
        {businessTeam && businessTeam.length > 0 ? (
          <TeamMembersTable
            teams={businessTeam} // IBusinessTeamRes[]
            mapper={(member) => ({
              photo: member.Photo,
              name: member.Name || 'N/A',
              email: member.Email || 'N/A',
              type: member.type,
              permissions: member.permissions,
              services: member.services,
            })}
            columns={[
              { label: 'Photo', render: m => <img src={m.photo} className="w-10 h-10 rounded-full" /> },
              { label: 'Name', render: m => m.name },
              { label: 'Email', render: m => m.email },
              { label: 'Type', render: m => m.type ?? 'N/A' },
              { label: 'Permissions', render: m => m.permissions?.split(',').join(', ') ?? 'N/A' },
              { label: 'Services', render: m => m.services ?? 'N/A' },
            ]}
            actions={
              [{ action: assignMember, label: "Assign" }]
            }
          />
        ) : (
          (
            <div className="text-center py-6">
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Team Members Available</h3>
              <p className="text-muted-foreground">
                No technicians have been assigned to this work order.
              </p>
            </div>
          )
        )}
        <DialogFooter>
          <Button onClick={() => setIsDialogOpen(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

interface DeleteTeamMemberDialogProps {
  setIsDeleteDialogOpen: (value: boolean) => void,
  team: ITeamMemberWorkOrderRes,
  removeMember: (team: ITeamMemberWorkOrderRes) => void
}
function DeleteTeamMemberDialog({ setIsDeleteDialogOpen, team, removeMember }: DeleteTeamMemberDialogProps): JSX.Element {
  console.log("Team To DELETE", team);
  return (
    <Dialog open={true} onOpenChange={setIsDeleteDialogOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Remove Team Member</DialogTitle>
        </DialogHeader>
        <p className="text-sm text-muted-foreground">
          Are you sure you want to remove this team member?
        </p>
        <div className="bg-muted/40 rounded-md p-4 text-sm space-y-2 border">
          <div>
            <span className="font-medium">Name:</span> {team.user.name}
          </div>
          <div>
            <span className="font-medium">Email:</span> {team.user.email}
          </div>
          <div>
            <span className="font-medium">Roles:</span> {team.user.roles}
          </div>
          <div>
            <span className="font-medium">Location:</span> {team.user.city}, {team.user.province}, {team.user.country}
          </div>
          <div>
            <span className="font-medium">Phone:</span> {team.user.phone}
          </div>
        </div>
        <DialogFooter>
          <Button onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
          <Button variant="destructive" onClick={() => removeMember(team)}>Remove</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}