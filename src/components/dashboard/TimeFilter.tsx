import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Clock } from "lucide-react";

export interface TimeFilterOption {
  value: string;
  label: string;
  days: number;
}

export const TIME_FILTER_OPTIONS: TimeFilterOption[] = [
  { value: "7d", label: "Last 7 days", days: 7 },
  { value: "30d", label: "Last 30 days", days: 30 },
  { value: "60d", label: "Last 60 days", days: 60 },
  { value: "90d", label: "Last 90 days", days: 90 },
];

interface TimeFilterProps {
  value: string;
  onValueChange: (value: string) => void;
  options?: TimeFilterOption[];
  placeholder?: string;
  className?: string;
  showIcon?: boolean;
}

export function TimeFilter({
  value,
  onValueChange,
  options = TIME_FILTER_OPTIONS,
  placeholder = "Select time range",
  className,
  showIcon = true
}: TimeFilterProps) {
  return (
    <div className={`flex items-center gap-2 ${className || ''}`}>
      {showIcon && <Clock className="h-4 w-4 text-muted-foreground" />}
      <Select value={value} onValueChange={onValueChange}>
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}

export function getDateRangeFromFilter(filterValue: string, previous: boolean = false): { startDate: Date; endDate: Date } {
  const option = TIME_FILTER_OPTIONS.find(opt => opt.value === filterValue);
  const days = option?.days || 30;
  
  const today = new Date();
  let endDate: Date;
  let startDate: Date;

  if (previous) {
    endDate = new Date(today);
    endDate.setDate(today.getDate() - days);

    startDate = new Date(endDate);
    startDate.setDate(endDate.getDate() - days);
  } else {
    endDate = today;
    startDate = new Date(today);
    startDate.setDate(endDate.getDate() - days);
  }
  
  return { startDate, endDate };
}

export function isDateInRange(date: string | Date, filterValue: string, previous: boolean = false): boolean {

  const { startDate, endDate } = getDateRangeFromFilter(filterValue, previous);
  const checkDate = typeof date === 'string' ? new Date(date) : date;
  
  return checkDate >= startDate && checkDate <= endDate;
}
