import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface PercentageMetricCardProps {
  title: string;
  value: number;
  subtitle?: string;
  trend?: {
    value: number;
    label: string;
    positive?: boolean;
  };
  icon?: React.ReactNode;
  className?: string;
  loading?: boolean;
  showAsPercentage?: boolean;
}

export function PercentageMetricCard({
  title,
  value,
  subtitle,
  trend,
  icon,
  className,
  loading = false,
  showAsPercentage = true
}: PercentageMetricCardProps) {
  const formatValue = (val: number) => {
    if (showAsPercentage) {
      return `${val.toFixed(1)}%`;
    }
    return val.toLocaleString();
  };

  return loading ? (
    (
      <Card className={cn("", className)}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
          {icon && <div className="h-4 w-4 text-muted-foreground">{icon}</div>}
        </CardHeader>
        <CardContent>
          <div className="h-8 w-16 bg-muted animate-pulse rounded"></div>
          {subtitle && <div className="h-4 w-20 bg-muted animate-pulse rounded mt-1"></div>}
        </CardContent>
      </Card>
    )
  ) : (
    <Card className={cn("", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon && <div className="h-4 w-4 text-muted-foreground">{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatValue(value)}</div>
        {subtitle && <p className="text-xs text-muted-foreground">{subtitle}</p>}
        {trend && (
          <div className="flex items-center pt-1">
            <Badge variant={trend.positive ? "default" : "destructive"} className="text-xs">
              {trend.positive ? "+" : ""}
              {trend.value}%
            </Badge>
            <span className="text-xs text-muted-foreground ml-2">{trend.label}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
