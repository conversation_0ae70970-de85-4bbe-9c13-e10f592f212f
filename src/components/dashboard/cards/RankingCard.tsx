import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export interface RankingItem {
  name: string
  label: string
  count: number | string
  trend?: {
    value: number
    positive: boolean
  }
}

interface RankingCardProps {
  title: string
  items: RankingItem[]
  className?: string
  loading?: boolean
}

export function RankingCard({ title, items, className, loading = false }: RankingCardProps) {
  if (loading) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="text-sm font-medium">{title}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-muted animate-pulse rounded"></div>
                <div className="space-y-1">
                  <div className="h-4 w-24 bg-muted animate-pulse rounded"></div>
                  <div className="h-3 w-16 bg-muted animate-pulse rounded"></div>
                </div>
              </div>
              <div className="h-6 w-12 bg-muted animate-pulse rounded"></div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("", className)}>
    <CardHeader>
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
    </CardHeader>
    <CardContent className="space-y-3">
      {items.map((item, index) => (
        <div key={item.name} className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
              {index + 1}
            </Badge>
            <div>
              <p className="text-sm font-medium">{item.name}</p>
              <p className="text-xs text-muted-foreground">
                {item.label}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="default">{item.count}</Badge>
            {item.trend && (
              <Badge
                variant={item.trend.positive ? "default" : "destructive"}
                className="text-xs px-1"
              >
                {item.trend.positive ? "+" : ""}{item.trend.value}%
              </Badge>
            )}
          </div>
        </div>
      ))}
    </CardContent>
  </Card>
  )
}