import { <PERSON><PERSON>, Clock, CheckCircle, Calendar, TrendingUp } from "lucide-react";
import { <PERSON><PERSON>ead<PERSON>, TimeFilter } from "./";
import { MetricCard, PercentageMetricCard, StatusBreakdown, RankingCard } from "./cards";

interface ServiceOperationsSectionProps {
  metrics: {
    workOrders: {
      active: number;
      completed: number;
      previousCompleted: number;
      scheduled: number;
      previousScheduled: number;
      total: number;
      statusBreakdown: { [key: string]: number };
    };
    technicians: {
      efficiency: {
        name: string;
        ordersCompleted: number;
        hoursWorked: number;
        efficiency: number;
      }[];
    };
  };
  timeFilter: string;
  onTimeFilterChange: (value: string) => void;
  loading: boolean;
}

export function ServiceOperationsSection({
  metrics,
  timeFilter,
  onTimeFilterChange,
  loading
}: ServiceOperationsSectionProps) {

  function getStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
    switch (status.toLowerCase()) {
      case 'completed': return 'default';
      case 'in progress': return 'secondary';
      case 'pending': return 'destructive';
      case 'scheduled': return 'outline';
      default: return 'outline';
    }
  }

  const statusBreakdownItems = Object.entries(metrics.workOrders.statusBreakdown).map(([status, count]) => ({
    label: status.charAt(0).toUpperCase() + status.slice(1),
    count,
    variant: getStatusVariant(status)
  }));


  const technicianRankingItems = metrics.technicians.efficiency.map(tech => ({
    name: tech.name,
    label: `${tech.ordersCompleted} orders • ${tech.hoursWorked}h`,
    count: `${tech.efficiency}%`
  }));

  return (
    <div className="space-y-6">
      <SectionHeader
        title="Service & Operations"
        description="Work order analytics and technician performance"
        icon={<Wrench className="h-4 w-4" />}
      >
        <TimeFilter
          value={timeFilter}
          onValueChange={onTimeFilterChange}
          showIcon={false}
        />
      </SectionHeader>

      {/* Primary Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Active Work Orders"
          value={metrics.workOrders.active}
          icon={<Wrench className="h-4 w-4" />}
          loading={loading}
        />

        <MetricCard
          title="Completed Orders"
          value={metrics.workOrders.completed}
          subtitle={`Total: ${metrics.workOrders.total}`}
          trend={
            {
              value: Math.round((metrics.workOrders.completed - metrics.workOrders.previousCompleted) / metrics.workOrders.previousCompleted * 100),
              label: "vs last period",
              positive: metrics.workOrders.completed > metrics.workOrders.previousCompleted
            }
          }
          icon={<CheckCircle className="h-4 w-4" />}
          loading={loading}
        />

        <MetricCard
          title="Scheduled Orders"
          value={metrics.workOrders.scheduled}
          subtitle="All upcoming"
          icon={<Calendar className="h-4 w-4" />}
          loading={loading}
        />

        <PercentageMetricCard
          title="Completion Rate"
          value={metrics.workOrders.total > 0 ? (metrics.workOrders.completed / metrics.workOrders.total) * 100 : 0}
          trend={{ value: 8, label: "vs last period", positive: true }}
          icon={<TrendingUp className="h-4 w-4" />}
          loading={loading}
        />
      </div>

      {/* Status Breakdown and Technician Rankings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <StatusBreakdown
          title="Work Order Status"
          items={statusBreakdownItems}
          total={metrics.workOrders.total}
          loading={loading}
        />

        <RankingCard
          title="Top Technicians"
          items={technicianRankingItems}
          loading={loading}
        />
      </div>
    </div>
  );
}
