import { Truck, Fuel, Wrench, DollarSign, TrendingUp, Calendar } from "lucide-react";
import { SectionHeader } from "./SectionHeader";
import { CurrencyMetricCard } from "./cards";
import { BarChartCard } from "./cards";
import { TimeFilter } from "./TimeFilter";
import { getDateRangeFromFilter } from "./TimeFilter";

interface VehicleCostSectionProps {
  timeFilter: string;
  onTimeFilterChange: (value: string) => void;
  loading: boolean;
}

// Mock vehicle cost data structure - can be easily replaced with real API calls
const generateMockVehicleData = (timeFilter: string) => {
  const { startDate, endDate } = getDateRangeFromFilter(timeFilter);
  const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Base costs that scale with time period
  const baseFuelCost = 2500;
  const baseMaintenanceCost = 1800;
  const baseRepairCost = 1200;
  const baseInsuranceCost = 800;
  
  // Scale costs based on time period (daily rate)
  const scaleFactor = days / 30; // Normalize to 30-day base
  
  return {
    totalCosts: {
      fuel: Math.round(baseFuelCost * scaleFactor),
      maintenance: Math.round(baseMaintenanceCost * scaleFactor),
      repairs: Math.round(baseRepairCost * scaleFactor),
      insurance: Math.round(baseInsuranceCost * scaleFactor),
    },
    trends: {
      fuel: Math.random() > 0.5 ? Math.floor(Math.random() * 15) + 1 : -(Math.floor(Math.random() * 10) + 1),
      maintenance: Math.random() > 0.5 ? Math.floor(Math.random() * 20) + 1 : -(Math.floor(Math.random() * 15) + 1),
      repairs: Math.random() > 0.5 ? Math.floor(Math.random() * 25) + 1 : -(Math.floor(Math.random() * 20) + 1),
      insurance: Math.random() > 0.5 ? Math.floor(Math.random() * 5) + 1 : -(Math.floor(Math.random() * 5) + 1),
    },
    chartData: Array.from({ length: Math.min(days, 10) }, (_, i) => ({
      period: `${i + 1}`,
      count: Math.floor(Math.random() * 500) + 200
    }))
  };
};

export function VehicleCostSection({
  timeFilter,
  onTimeFilterChange,
  loading
}: VehicleCostSectionProps) {
  const vehicleData = generateMockVehicleData(timeFilter);
  const totalVehicleCosts = Object.values(vehicleData.totalCosts).reduce((sum, cost) => sum + cost, 0);

  return (
    <div className="space-y-6">
      <SectionHeader
        title="Vehicle Cost Tracking"
        description="Vehicle-related expenses and maintenance costs"
        icon={<Truck className="h-4 w-4" />}
      >
        <TimeFilter
          value={timeFilter}
          onValueChange={onTimeFilterChange}
          showIcon={false}
        />
      </SectionHeader>

      {/* Total Vehicle Costs Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <CurrencyMetricCard
          title="Total Vehicle Costs"
          value={totalVehicleCosts}
          trend={{ 
            value: Math.floor((vehicleData.trends.fuel + vehicleData.trends.maintenance) / 2), 
            label: "vs last period", 
            positive: vehicleData.trends.fuel + vehicleData.trends.maintenance > 0 
          }}
          icon={<DollarSign className="h-4 w-4" />}
          loading={loading}
        />
        
        <CurrencyMetricCard
          title="Fuel Costs"
          value={vehicleData.totalCosts.fuel}
          trend={{ 
            value: vehicleData.trends.fuel, 
            label: "vs last period", 
            positive: vehicleData.trends.fuel > 0 
          }}
          icon={<Fuel className="h-4 w-4" />}
          loading={loading}
        />
        
        <CurrencyMetricCard
          title="Maintenance"
          value={vehicleData.totalCosts.maintenance}
          trend={{ 
            value: vehicleData.trends.maintenance, 
            label: "vs last period", 
            positive: vehicleData.trends.maintenance > 0 
          }}
          icon={<Wrench className="h-4 w-4" />}
          loading={loading}
        />
        
        <CurrencyMetricCard
          title="Repairs"
          value={vehicleData.totalCosts.repairs}
          trend={{ 
            value: vehicleData.trends.repairs, 
            label: "vs last period", 
            positive: vehicleData.trends.repairs > 0 
          }}
          icon={<Wrench className="h-4 w-4" />}
          loading={loading}
        />
        
        <CurrencyMetricCard
          title="Insurance"
          value={vehicleData.totalCosts.insurance}
          trend={{ 
            value: vehicleData.trends.insurance, 
            label: "vs last period", 
            positive: vehicleData.trends.insurance > 0 
          }}
          icon={<Calendar className="h-4 w-4" />}
          loading={loading}
        />
      </div>

      {/* Vehicle Cost Trends Chart */}
      <div className="grid grid-cols-1 gap-6">
        <BarChartCard
          title="Vehicle Expenses Over Time"
          description={`Daily vehicle costs for selected period`}
          chartConfig={{ label: "Daily Costs ($)", color: "#8b5cf6" }}
          data={vehicleData.chartData}
        />
      </div>

      {/* Additional Vehicle Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <CurrencyMetricCard
          title="Average Daily Cost"
          value={Math.round(totalVehicleCosts / Math.max(1, vehicleData.chartData.length))}
          subtitle="Per vehicle per day"
          icon={<TrendingUp className="h-4 w-4" />}
          loading={loading}
        />
        
        <CurrencyMetricCard
          title="Fuel Efficiency Cost"
          value={Math.round(vehicleData.totalCosts.fuel / Math.max(1, vehicleData.chartData.length))}
          subtitle="Daily fuel average"
          icon={<Fuel className="h-4 w-4" />}
          loading={loading}
        />
        
        <CurrencyMetricCard
          title="Maintenance Rate"
          value={Math.round((vehicleData.totalCosts.maintenance + vehicleData.totalCosts.repairs) / Math.max(1, vehicleData.chartData.length))}
          subtitle="Daily maintenance average"
          icon={<Wrench className="h-4 w-4" />}
          loading={loading}
        />
      </div>
    </div>
  );
}
