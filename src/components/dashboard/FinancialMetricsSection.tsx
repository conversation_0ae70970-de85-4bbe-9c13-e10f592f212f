import { DollarSign, Users, FileText, TrendingUp, Package, AlertCircle } from "lucide-react";
import { MetricCard, CurrencyMetricCard, StatusBreakdown, CircularDiagram } from "./cards";
import { SectionHeader, TimeFilter } from "./";
interface FinancialMetricsSectionProps {
  metrics: {
    invoices: {
      total: number;
      paid: number;
      unpaid: number;
      overdue: number;
      totalValue: number;
      paidValue: number;
      unpaidValue: number;
      statusBreakdown: { [key: string]: number };
    };
    customers: {
      total: number;
    };
  };
  timeFilter: string;
  onTimeFilterChange: (value: string) => void;
  loading: boolean;
}

export function FinancialMetricsSection({
  metrics,
  timeFilter,
  onTimeFilterChange,
  loading
}: FinancialMetricsSectionProps) {
  // Convert invoice status breakdown to StatusBreakdown format
  const invoiceStatusItems = Object.entries(metrics.invoices.statusBreakdown).map(([status, count]) => ({
    label: status.charAt(0).toUpperCase() + status.slice(1),
    count,
    variant: getInvoiceStatusVariant(status)
  }));

  function getInvoiceStatusVariant(status: string): "default" | "secondary" | "destructive" | "outline" {
    switch (status.toLowerCase()) {
      case 'paid': return 'default';
      case 'unpaid': return 'secondary';
      case 'overdue': return 'destructive';
      case 'draft': return 'outline';
      default: return 'outline';
    }
  }

  const invoiceStatusChartData = [
    { name: "Paid", value: metrics.invoices.paid, color: "#4ade80" },
    { name: "Unpaid", value: metrics.invoices.unpaid, color: "#facc15" },
    { name: "Overdue", value: metrics.invoices.overdue, color: "#f87171" },
  ].filter(item => item.value > 0);

  const paymentRate = metrics.invoices.total > 0 
    ? (metrics.invoices.paid / metrics.invoices.total) * 100 
    : 0;

  return (
    <div className="space-y-6">
      <SectionHeader
        title="Financial Metrics"
        description="Revenue, invoicing, and inventory analytics"
        icon={<DollarSign className="h-4 w-4" />}
      >
        <TimeFilter
          value={timeFilter}
          onValueChange={onTimeFilterChange}
          showIcon={false}
        />
      </SectionHeader>

      {/* Primary Financial Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Customers"
          value={metrics.customers.total}
          icon={<Users className="h-4 w-4" />}
          loading={loading}
        />
        
        <MetricCard
          title="Total Invoices"
          value={metrics.invoices.total}
          subtitle={`${metrics.invoices.paid} paid, ${metrics.invoices.unpaid} unpaid`}
          icon={<FileText className="h-4 w-4" />}
          loading={loading}
        />
        
        <MetricCard
          title="Overdue Invoices"
          value={metrics.invoices.overdue}
          trend={{ value: -15, label: "vs last period", positive: false }}
          icon={<AlertCircle className="h-4 w-4" />}
          loading={loading}
        />

        <MetricCard
          title="Payment Rate"
          value={`${paymentRate.toFixed(1)}%`}
          trend={{ value: 5, label: "vs last period", positive: true }}
          icon={<TrendingUp className="h-4 w-4" />}
          loading={loading}
        />
      </div>

      {/* Revenue Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <CurrencyMetricCard
          title="Total Invoice Value"
          value={metrics.invoices.totalValue}
          trend={{ value: 12, label: "vs last period", positive: true }}
          icon={<DollarSign className="h-4 w-4" />}
          loading={loading}
        />
        
        <CurrencyMetricCard
          title="Paid Amount"
          value={metrics.invoices.paidValue}
          subtitle="Collected revenue"
          trend={{ value: 18, label: "vs last period", positive: true }}
          icon={<DollarSign className="h-4 w-4" />}
          loading={loading}
        />
        
        <CurrencyMetricCard
          title="Outstanding Amount"
          value={metrics.invoices.unpaidValue}
          subtitle="Pending collection"
          trend={{ value: -8, label: "vs last period", positive: false }}
          icon={<DollarSign className="h-4 w-4" />}
          loading={loading}
        />
      </div>

      {/* Invoice Status Breakdown and Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <StatusBreakdown
          title="Invoice Status Breakdown"
          items={invoiceStatusItems}
          total={metrics.invoices.total}
          loading={loading}
        />
        
        <CircularDiagram
          title="Invoice Status Distribution"
          description={`Total: ${metrics.invoices.total} invoices`}
          statusData={invoiceStatusChartData}
        />
      </div>
    </div>
  );
}
