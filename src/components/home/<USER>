
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export function HeroSection() {
  return (
    <section className="relative overflow-hidden py-20 md:py-32 hero-gradient">
      {/* Background abstract shapes */}
      <div className="absolute inset-0 z-0">
        <svg width="500" height="500" viewBox="0 0 500 500" className="animated-shape opacity-10 right-[-100px] top-[-100px]" xmlns="http://www.w3.org/2000/svg">
          <circle cx="250" cy="250" r="200" className="fill-primary" />
        </svg>
        <svg width="400" height="400" viewBox="0 0 400 400" className="animated-shape opacity-10 left-[-150px] bottom-[-150px] animation-delay-1000" xmlns="http://www.w3.org/2000/svg">
          <rect x="50" y="50" width="300" height="300" rx="30" className="fill-accent" />
        </svg>
      </div>

      <div className="container relative z-10">
        <div className="grid gap-12 items-center lg:grid-cols-2">
          <div className="space-y-8 text-center lg:text-left">
            <div>
              <h1 className="text-4xl md:text-5xl font-extrabold tracking-tight">
                Manage your service <br className="hidden md:inline" />
                <span className="text-primary">business easily.</span>
              </h1>
              <p className="mt-4 text-lg text-muted-foreground max-w-xl mx-auto lg:mx-0">
                OpenZcan helps you manage your service business more effectively. 
                With AI powered systems, OpenZcan delivers results faster than ever before.
              </p>
            </div>
            
          </div>

          <div className="relative mx-auto lg:mr-0">

            <img src="/OZ-work-orders.png" alt="Technician working" />
           
          </div>
        </div>
      </div>
    </section>
  );
}
