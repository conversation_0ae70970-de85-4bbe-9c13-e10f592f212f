
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Users, FileText, Receipt, User, Package, Box } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const FeatureCard = ({ icon, title, description }: FeatureCardProps) => (
  <Card className="feature-card bg-card">
    <CardHeader>
      <div className="h-12 w-12 rounded-lg bg-primary/10 dark:bg-primary/20 flex items-center justify-center mb-4">
        {icon}
      </div>
      <CardTitle>{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <CardDescription className="text-base">{description}</CardDescription>
    </CardContent>
  </Card>
);

const features = [
  {
    icon: <Users className="h-6 w-6 text-primary" />,
    title: "Customers",
    description: "Manage customer profiles, service history, and preferences efficiently from one central location."
  },
  {
    icon: <FileText className="h-6 w-6 text-primary" />,
    title: "Work Orders",
    description: "Create, assign, and track detailed work orders with real-time status updates and notifications."
  },
  {
    icon: <Receipt className="h-6 w-6 text-primary" />,
    title: "Invoices",
    description: "Generate professional invoices automatically from completed work orders with customizable templates."
  },
  {
    icon: <User className="h-6 w-6 text-primary" />,
    title: "Team",
    description: "Optimize technician scheduling, track performance, and manage certifications all in one place."
  },
  {
    icon: <Package className="h-6 w-6 text-primary" />,
    title: "Inventory",
    description: "Keep track of parts and equipment with automated alerts for low stock and streamlined reordering."
  },
  {
    icon: <Box className="h-6 w-6 text-primary" />,
    title: "Bins",
    description: "Organize storage locations for efficient inventory management and faster part retrieval."
  }
];

export function FeatureSection() {
  return (
    <section className="py-20">
      <div className="container">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold tracking-tight">
            Streamline Your Service Operations
          </h2>
          <p className="mt-4 text-lg text-muted-foreground max-w-2xl mx-auto">
            OpenZcan provides powerful tools designed specifically for service businesses to help you manage every aspect of your operations.
          </p>
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              icon={feature.icon}
              title={feature.title}
              description={feature.description}
            />
          ))}
        </div>

        {/* Interface Preview SVG */}
        <div className="mt-24 relative bg-secondary/50 rounded-xl p-6 md:p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-2">Intuitive Interface</h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Our clean, modern interface helps your team work efficiently without the learning curve.
            </p>
          </div>
          
          <div className="flex flex-col md:flex-row gap-8 items-center justify-center">
            <div className="relative w-full max-w-md">
              <svg className="w-full" viewBox="0 0 400 300" fill="none" xmlns="http://www.w3.org/2000/svg">
                {/* UI Frame */}
                <rect x="10" y="10" width="380" height="280" rx="8" className="fill-background stroke-border" strokeWidth="2" />
                
                {/* Header */}
                <rect x="10" y="10" width="380" height="40" rx="8" className="fill-primary" />
                <circle cx="30" cy="30" r="6" fill="white" opacity="0.5" />
                <circle cx="50" cy="30" r="6" fill="white" opacity="0.5" />
                <circle cx="70" cy="30" r="6" fill="white" opacity="0.5" />
                
                {/* Sidebar */}
                <rect x="10" y="50" width="80" height="240" className="fill-muted/20" />
                <rect x="20" y="60" width="60" height="8" rx="4" className="fill-primary/60" />
                <rect x="20" y="80" width="60" height="8" rx="4" className="fill-muted-foreground/40" />
                <rect x="20" y="100" width="60" height="8" rx="4" className="fill-muted-foreground/40" />
                <rect x="20" y="120" width="60" height="8" rx="4" className="fill-muted-foreground/40" />
                
                {/* Content */}
                <rect x="100" y="60" width="100" height="15" rx="2" className="fill-foreground/90" />
                
                {/* Cards */}
                <rect x="100" y="85" width="130" height="90" rx="4" className="fill-card stroke-border" strokeWidth="1" />
                <rect x="110" y="95" width="110" height="10" rx="2" className="fill-primary/80" />
                <rect x="110" y="115" width="110" height="6" rx="2" className="fill-muted-foreground/30" />
                <rect x="110" y="125" width="80" height="6" rx="2" className="fill-muted-foreground/30" />
                <rect x="110" y="155" width="50" height="10" rx="2" className="fill-accent/80" />
                
                <rect x="240" y="85" width="130" height="90" rx="4" className="fill-card stroke-border" strokeWidth="1" />
                <rect x="250" y="95" width="110" height="10" rx="2" className="fill-primary/80" />
                <rect x="250" y="115" width="110" height="6" rx="2" className="fill-muted-foreground/30" />
                <rect x="250" y="125" width="80" height="6" rx="2" className="fill-muted-foreground/30" />
                <rect x="250" y="155" width="50" height="10" rx="2" className="fill-accent/80" />
                
                <rect x="100" y="185" width="270" height="90" rx="4" className="fill-card stroke-border" strokeWidth="1" />
                <rect x="110" y="195" width="150" height="10" rx="2" className="fill-primary/80" />
                <circle cx="340" cy="200" r="15" className="fill-accent/30" />
                
                <rect x="110" y="215" width="250" height="6" rx="2" className="fill-muted-foreground/30" />
                <rect x="110" y="225" width="200" height="6" rx="2" className="fill-muted-foreground/30" />
                <rect x="110" y="235" width="220" height="6" rx="2" className="fill-muted-foreground/30" />
                
                <rect x="110" y="255" width="60" height="10" rx="2" className="fill-primary" />
              </svg>
            </div>
            
            <div className="w-full max-w-md">
            <Card className="p-4 space-y-4">
  <CardHeader>
    <CardTitle>Work Order #2587</CardTitle>
    <CardDescription>
      Scheduled for April 30, 2025
    </CardDescription>
  </CardHeader>
  <CardContent>
    <div className="space-y-2">
      <div className="flex justify-between">
        <span>Customer:</span>
        <span className="font-medium">
          Acme Industries
        </span>
      </div>
      <div className="flex justify-between">
        <span>Technician:</span>
        <span className="font-medium">
          John Rodriguez
        </span>
      </div>
      <div className="flex justify-between">
        <span>Status:</span>
        <Badge>In Progress</Badge>
      </div>
    </div>
  </CardContent>
</Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
