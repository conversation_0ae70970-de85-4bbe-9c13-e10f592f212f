
import { Button } from "@/components/ui/button";

export function CTASection() {
  return (
    <section className="py-20 bg-primary/5">
      <div className="container">
        <div className="max-w-4xl mx-auto text-center">
          <svg
            width="100"
            height="100"
            viewBox="0 0 100 100"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="mx-auto mb-8"
          >
            <circle cx="50" cy="50" r="45" className="stroke-primary" strokeWidth="2" strokeDasharray="6 4" />
            <circle cx="50" cy="50" r="30" className="fill-primary/10" />
            <path
              d="M38 50L46 58L62 42"
              className="stroke-primary"
              strokeWidth="4"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>

          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Manage with clarity, service with confidence
          </h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            OpenZcan has your back.  We are here to help you boost your bottom line with our comprehensive service management platform.
          </p>
          
        </div>
      </div>
    </section>
  );
}
