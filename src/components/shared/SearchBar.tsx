import { Search, Loader2, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useState, useEffect, useMemo } from "react";
import { cn } from "@/lib/utils";

/**
 * Reusable SearchBar component that supports both client-side filtering and API-based search
 *
 * @example
 * // Client-side search
    import { SearchBar } from '@/components/shared/SearchBar';

    interface User {
      id: number;
      name: string;
      email: string;
      phone: string;
    }

    function UserList() {
      const [users] = useState<User[]>([...]);
      const [filteredUsers, setFilteredUsers] = useState<User[]>(users);

      return (
        <div>
          <SearchBar<User>
            config={{
              searchFields: ['name', 'email', 'phone'],
              placeholder: "Search users...",
              minSearchLength: 2
            }}
            items={users}
            onResults={(results) => setFilteredUsers(results)}
          />
          
          {filteredUsers.map(user => (
            <div key={user.id}>{user.name}</div>
          ))}
        </div>
      );
    }
 *
 * @example
 * // API-based search
    function ApiUserSearch() {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [results, setResults] = useState<User[]>([]);

    const handleSearch = async (searchTerm: string) => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/users/search?q=${searchTerm}`);
        const data = await response.json();
        setResults(data);
      } catch (err) {
        setError('Search failed');
      } finally {
        setLoading(false);
      }
    };

    return (
      <SearchBar<User>
        config={{
          searchFields: ['name', 'email'], // Required for TypeScript, not used in API mode
          placeholder: "Search users via API...",
          minSearchLength: 3,
          debounceDelay: 500
        }}
        onSearch={handleSearch}
        loading={loading}
        error={error}
      />
    );
  }
 */

export interface SearchConfig<T> {
  searchFields: (keyof T)[];
  minSearchLength?: number;
  debounceDelay?: number;
  placeholder?: string;
  caseSensitive?: boolean;
}

export interface SearchBarProps<T> {
  config: SearchConfig<T>;
  items?: T[];
  /** Callback function that receives filtered results (for client-side search) */
  onResults?: (results: T[], searchTerm: string) => void;
  /** Callback function for API-based search */
  onSearch?: (searchTerm: string) => Promise<T[]> | void;
  loading?: boolean;
  error?: string | null;
  className?: string;
  showClearButton?: boolean;
  searchIcon?: React.ReactNode;
  value?: string;
  onChange?: (searchTerm: string) => void;
}
export function SearchBar<T>({
  config,
  items,
  onResults,
  onSearch,
  loading = false,
  error = null,
  className,
  showClearButton = true,
  searchIcon,
  value,
  onChange
}: SearchBarProps<T>): JSX.Element {
  const [internalSearchTerm, setInternalSearchTerm] = useState<string>("");

  const searchTerm = value !== undefined ? value : internalSearchTerm;

  const {
    searchFields,
    minSearchLength = 2,
    debounceDelay = 300,
    placeholder = "Search...",
    caseSensitive = false
  } = config;

  // Client-side filtering logic
  const filteredResults = useMemo(() => {
    if (!items || searchTerm.length < minSearchLength) {
      return items || [];
    }

    const searchLower = caseSensitive ? searchTerm : searchTerm.toLowerCase();

    return items.filter((item) => {
      return searchFields.some((field) => {
        const fieldValue = item[field];
        if (fieldValue == null) return false;

        const stringValue = String(fieldValue);
        const compareValue = caseSensitive ? stringValue : stringValue.toLowerCase();

        // Handle array fields (like flags)
        if (Array.isArray(fieldValue)) {
          return fieldValue.some(arrayItem => {
            const arrayStringValue = String(arrayItem);
            const arrayCompareValue = caseSensitive ? arrayStringValue : arrayStringValue.toLowerCase();
            return arrayCompareValue.includes(searchLower);
          });
        }

        return compareValue.includes(searchLower);
      });
    });
  }, [items, searchTerm, searchFields, minSearchLength, caseSensitive]);

  const handleSearchChange = (newSearchTerm: string) => {
    if (onChange) {
      onChange(newSearchTerm);
    } else {
      setInternalSearchTerm(newSearchTerm);
    }
  };

  const handleClear = () => {
    handleSearchChange("");
  };

  // Debounced effect for client-side results
  useEffect(() => {
    if (onResults && items) {
      const timer = setTimeout(() => {
        onResults(filteredResults, searchTerm);
      }, debounceDelay);

      return () => clearTimeout(timer);
    }
  }, [filteredResults, searchTerm, onResults, debounceDelay, items]);

  // Debounced effect for API-based search
  useEffect(() => {
    if (onSearch && searchTerm.length >= minSearchLength) {
      const timer = setTimeout(() => {
        onSearch(searchTerm);
      }, debounceDelay);

      return () => clearTimeout(timer);
    }
  }, [searchTerm, onSearch, minSearchLength, debounceDelay]);

  return (
    <div className={cn("space-y-2", className)}>
      <div className="relative">
        {/* Search Icon */}
        <div className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground">
          {searchIcon || <Search className="h-4 w-4" />}
        </div>

        {/* Search Input */}
        <Input
          type="text"
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          placeholder={placeholder}
          className={cn(
            "pl-9",
            showClearButton && searchTerm && "pr-20",
            loading && "pr-9"
          )}
        />

        {/* Loading Indicator */}
        {loading && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        )}

        {/* Clear Button */}
        {showClearButton && searchTerm && !loading && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 h-7 w-7 -translate-y-1/2 p-0 hover:bg-muted"
            onClick={handleClear}
            type="button"
          >
            <X className="h-3 w-3" />
            <span className="sr-only">Clear search</span>
          </Button>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="text-sm text-destructive">
          {error}
        </div>
      )}
    </div>
  );
}
