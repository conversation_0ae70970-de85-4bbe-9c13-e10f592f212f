import { IRelatedUser } from './';
import { IRelatedLocation } from './';

export interface IProviderRes {
  Configs: any; //Update
  Contacts: any | null; //Update
  Contractors: any | null; //Update
  CreatedAt: string;
  Customers: any | null;
  Roles: any; //Update 
  Subscriptions: any;
  UpdatedAt: string;
  UpdatedBy: number;
  User: IRelatedUser;
  accountName: string;
  accountNumber: string;
  address: string;
  background: string;
  bank: string;
  banner: string;
  categories: any | null;
  city: string;
  colour: string;
  country: string;
  currency: string;
  description: string;
  email: string;
  enabled: boolean;
  facebook: string;
  fields: any | null;
  flags: any | null;
  id: number;
  instagram: string;
  latlng: string;
  locale: string;
  locations: IRelatedLocation[];
  name: string;
  nid: string;
  paymentTerms: string;
  phone: string;
  photo: string;
  providerId: number;
  province: string;
  rating: string;
  session: string;
  signal: string;
  sortCode: string;
  style: string;
  summary: string;
  taxId: string;
  telegram: string;
  twitter: string;
  type: string;
  uid: string;
  userId: number;
  uuid: string;
  website: string;
  whatsapp: string;
  youtube: string;
  zipcode: string;
}

export interface IProviderRelatedRes extends IProviderRes { }