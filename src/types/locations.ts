export interface IRelatedLocation{
  id: number;
  uid: string;
  uuid: string;
  name: string;
  address: string;
  city: string;
  province: string;
  postcode: string;
  country: string;
  contactName: string;
  phone: string;
  whatsapp: string;
  telegram: string;
  email: string;
  website: string;
  identity: string;
  latlng: string;
  message: string;
  signal: string;
  session: string;
  photo: string;
  rank: number;
  flags: string[] | null;
  businessId: number;
  userId: number;
  Business: null;
  User: null;
  Configs: null;
  Equipment: any[]; // TODO: change to IEquipmentRes[] when necessary
  CreatedAt: string;
  UpdatedAt: string;
  UpdatedBy: number;
}

export interface IStateRes {
  code: string;
  state: string;
}

export interface ICityRes{
  id: number;
  code: string;
  state: string;
  city: string;
  county: string;
  latitude: string;
  longitude: string;
  country: string;
}