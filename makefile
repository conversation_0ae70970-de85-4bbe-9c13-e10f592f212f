
mkdirs:
	ssh damian@osvps1 "mkdir -p /opt/www/Openscan/ui/current/public"
	ssh damian@osvps2 "mkdir -p /opt/www/Openscan/ui/current/public"
	ssh damian@osvps1 "mkdir -p /opt/www/Openscan/dev/current/public"
	ssh damian@osvps2 "mkdir -p /opt/www/Openscan/dev/current/public"


build:
	pnpm build

deploy: mkdirs build
	scp -r dist/* damian@osvps1:/opt/www/Openscan/ui/current/public
	scp -r dist/* damian@osvps2:/opt/www/Openscan/ui/current/public
	scp -r dist/* damian@osvps1:/opt/www/Openscan/dev/current/public
	scp -r dist/* damian@osvps2:/opt/www/Openscan/dev/current/public

release: build deploy